#!/usr/bin/env node

// 简单的插件系统测试脚本
console.log('🚀 启动插件系统测试...')

// 设置环境变量
process.env.NEXT_PUBLIC_USE_PLUGIN_SYSTEM = 'true'

console.log('✅ 环境变量已设置')
console.log('📝 请访问 http://localhost:3000/test-plugins 查看测试结果')
console.log('📝 或者访问主页面 http://localhost:3000 体验插件化编辑器')

// 如果是在开发环境中运行
if (process.argv.includes('--dev')) {
  const { spawn } = require('child_process')
  
  console.log('🔧 启动开发服务器...')
  const devServer = spawn('npm', ['run', 'dev'], {
    stdio: 'inherit',
    env: {
      ...process.env,
      NEXT_PUBLIC_USE_PLUGIN_SYSTEM: 'true'
    }
  })

  devServer.on('close', (code) => {
    console.log(`开发服务器退出，代码: ${code}`)
  })
}
