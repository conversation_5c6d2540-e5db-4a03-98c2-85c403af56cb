// 动作选项面板组件
'use client'

import React from 'react'
import { But<PERSON> } from '../ui/button'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import { Slider } from '../ui/slider'
import { Separator } from '../ui/separator'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select'
import { Checkbox } from '../ui/checkbox'
import { Loader2, Play, X } from 'lucide-react'
import type { ActionOption } from '../plugins/types'

interface ActionOptionsPanelProps {
  options: ActionOption[]
  values: Record<string, any>
  onChange: (values: Record<string, any>) => void
  onExecute: (values: Record<string, any>) => Promise<void>
  onCancel: () => void
  disabled: boolean
  isProcessing: boolean
}

export const ActionOptionsPanel: React.FC<ActionOptionsPanelProps> = ({
  options,
  values,
  onChange,
  onExecute,
  onCancel,
  disabled,
  isProcessing
}) => {
  // 更新单个选项值
  const updateValue = (key: string, value: any) => {
    onChange({
      ...values,
      [key]: value
    })
  }

  // 执行动作
  const handleExecute = async () => {
    try {
      await onExecute(values)
      onCancel() // 执行成功后关闭面板
    } catch (error) {
      console.error('Failed to execute action:', error)
    }
  }

  // 渲染选项控件
  const renderOptionControl = (option: ActionOption) => {
    const value = values[option.key] ?? option.defaultValue

    switch (option.type) {
      case 'string':
        return (
          <Input
            value={value || ''}
            onChange={(e) => updateValue(option.key, e.target.value)}
            placeholder={option.description}
            disabled={disabled}
          />
        )

      case 'number':
        return (
          <Input
            type="number"
            value={value || 0}
            onChange={(e) => updateValue(option.key, parseFloat(e.target.value) || 0)}
            min={option.min}
            max={option.max}
            step={option.step}
            disabled={disabled}
          />
        )

      case 'boolean':
        return (
          <div className="flex items-center space-x-2">
            <Checkbox
              id={option.key}
              checked={value || false}
              onCheckedChange={(checked) => updateValue(option.key, checked)}
              disabled={disabled}
            />
            <Label htmlFor={option.key} className="text-sm">
              {option.description || '启用'}
            </Label>
          </div>
        )

      case 'select':
        return (
          <Select
            value={value || option.defaultValue}
            onValueChange={(newValue) => updateValue(option.key, newValue)}
            disabled={disabled}
          >
            <SelectTrigger>
              <SelectValue placeholder="请选择..." />
            </SelectTrigger>
            <SelectContent>
              {option.options?.map((opt) => (
                <SelectItem key={opt.value} value={opt.value}>
                  {opt.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )

      case 'slider':
        return (
          <div className="space-y-2">
            <div className="flex justify-between text-sm text-gray-600">
              <span>{option.min || 0}</span>
              <span className="font-medium">{value}</span>
              <span>{option.max || 100}</span>
            </div>
            <Slider
              value={[value || option.defaultValue]}
              onValueChange={(newValue) => updateValue(option.key, newValue[0])}
              min={option.min || 0}
              max={option.max || 100}
              step={option.step || 1}
              disabled={disabled}
              className="w-full"
            />
          </div>
        )

      default:
        return (
          <Input
            value={value || ''}
            onChange={(e) => updateValue(option.key, e.target.value)}
            disabled={disabled}
          />
        )
    }
  }

  return (
    <div className="border border-gray-200 rounded-lg p-4 bg-gray-50 space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="font-medium text-gray-800">动作选项</h4>
        <Button
          variant="ghost"
          size="sm"
          onClick={onCancel}
          disabled={isProcessing}
        >
          <X className="w-4 h-4" />
        </Button>
      </div>

      <Separator />

      {/* 选项列表 */}
      <div className="space-y-4">
        {options.map((option) => (
          <div key={option.key} className="space-y-2">
            <Label className="text-sm font-medium text-gray-700">
              {option.name}
            </Label>
            {renderOptionControl(option)}
            {option.description && option.type !== 'boolean' && (
              <p className="text-xs text-gray-500">
                {option.description}
              </p>
            )}
          </div>
        ))}
      </div>

      <Separator />

      {/* 操作按钮 */}
      <div className="flex gap-2">
        <Button
          onClick={handleExecute}
          disabled={disabled || isProcessing}
          className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
        >
          {isProcessing ? (
            <>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              处理中...
            </>
          ) : (
            <>
              <Play className="w-4 h-4 mr-2" />
              执行
            </>
          )}
        </Button>
        <Button
          variant="outline"
          onClick={onCancel}
          disabled={isProcessing}
          className="border-gray-300"
        >
          取消
        </Button>
      </div>

      {/* 预设选项（如果有的话） */}
      {options.some(opt => opt.type === 'select' && opt.options && opt.options.length > 3) && (
        <>
          <Separator />
          <div className="space-y-2">
            <Label className="text-sm font-medium text-gray-700">
              快速预设
            </Label>
            <div className="flex flex-wrap gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  // 重置为默认值
                  const defaultValues: Record<string, any> = {}
                  options.forEach(opt => {
                    defaultValues[opt.key] = opt.defaultValue
                  })
                  onChange(defaultValues)
                }}
                disabled={disabled}
                className="text-xs"
              >
                默认
              </Button>
              {/* 可以添加更多预设 */}
            </div>
          </div>
        </>
      )}
    </div>
  )
}
