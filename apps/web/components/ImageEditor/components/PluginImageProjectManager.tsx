// 插件化图片项目管理器
'use client'

import { useState, useCallback, useEffect } from 'react'
import { PluginManager } from '../plugins/PluginManager'
import { builtinPlugins, defaultPluginConfigs } from '../plugins/builtin'
import type { ImageData } from '../index'
import type { IPluginManager, ProcessingResult } from '../plugins/types'

export interface PluginImageProject {
  images: ImageData[]
  currentImageId: string | null
  pluginManager: IPluginManager
}

export const usePluginImageProjectManager = () => {
  const [project, setProject] = useState<PluginImageProject>(() => {
    const pluginManager = new PluginManager()
    return {
      images: [],
      currentImageId: null,
      pluginManager,
    }
  })

  // 初始化插件系统
  useEffect(() => {
    const initializePlugins = async () => {
      try {
        // 注册内置插件
        for (const plugin of builtinPlugins) {
          const config =
            defaultPluginConfigs[plugin.id as keyof typeof defaultPluginConfigs]
          await project.pluginManager.register(plugin, config)
        }

        // 初始化插件管理器
        await project.pluginManager.initialize()

        console.log('Plugin system initialized successfully')
      } catch (error) {
        console.error('Failed to initialize plugin system:', error)
      }
    }

    initializePlugins()

    // 清理函数
    return () => {
      project.pluginManager.destroy()
    }
  }, []) // 只在组件挂载时执行一次

  // 监听插件事件
  useEffect(() => {
    const unsubscribers: (() => void)[] = []

    // 监听UI相关事件
    unsubscribers.push(
      project.pluginManager.on('ui:toast', (data) => {
        // 这里可以集成到全局toast系统
        console.log('Toast:', data)
      })
    )

    unsubscribers.push(
      project.pluginManager.on('ui:modal', (data) => {
        // 这里可以集成到全局modal系统
        console.log('Modal:', data)
      })
    )

    // 监听mask canvas请求
    unsubscribers.push(
      project.pluginManager.on('request:mask-canvas', (callback) => {
        // 这里需要从CanvasEditor获取mask canvas
        // 暂时返回null，实际实现时需要通过ref获取
        callback(null)
      })
    )

    return () => {
      unsubscribers.forEach((unsub) => unsub())
    }
  }, [project.pluginManager])

  // Helper functions
  const getCurrentImage = useCallback((): ImageData | null => {
    if (!project.currentImageId) return null
    return (
      project.images.find((img) => img.id === project.currentImageId) || null
    )
  }, [project.currentImageId, project.images])

  const getCurrentResult = useCallback((): ProcessingResult | null => {
    return project.pluginManager.getLatestResult()
  }, [project.pluginManager])

  // 获取最终结果URL（用于显示）
  const getCurrentFinalResult = useCallback((): {
    url: string | null
    type: 'none' | 'inpaint' | 'background' | 'blur' | 'final'
  } => {
    const result = getCurrentResult()
    if (result) {
      // 将插件动作映射到原有的类型系统
      let type: 'none' | 'inpaint' | 'background' | 'blur' | 'final' = 'final'

      if (result.actionId === 'remove-objects') {
        type = 'inpaint'
      } else if (result.actionId.includes('background')) {
        if (result.actionId.includes('blur')) {
          type = 'blur'
        } else {
          type = 'background'
        }
      }

      return {
        url: result.imageUrl,
        type,
      }
    }
    return { url: null, type: 'none' }
  }, [getCurrentResult])

  // Image management
  const addImage = useCallback((imageData: ImageData) => {
    setProject((prev) => {
      const newProject = {
        ...prev,
        images: [...prev.images, imageData],
        currentImageId: imageData.id,
      }

      // 通知插件管理器图片变化
      newProject.pluginManager.setCurrentImage(imageData)

      return newProject
    })
  }, [])

  const addImages = useCallback((images: ImageData[]) => {
    setProject((prev) => {
      const newProject = {
        ...prev,
        images: [...prev.images, ...images],
        currentImageId: images[0]?.id || prev.currentImageId,
      }

      // 通知插件管理器图片变化
      const currentImage = images[0] || null
      if (currentImage) {
        newProject.pluginManager.setCurrentImage(currentImage)
      }

      return newProject
    })
  }, [])

  const removeImage = useCallback((imageId: string) => {
    setProject((prev) => {
      const newImages = prev.images.filter((img) => img.id !== imageId)

      // If removing current image, select another one
      let newCurrentImageId = prev.currentImageId
      if (prev.currentImageId === imageId) {
        newCurrentImageId = newImages.length > 0 ? newImages[0].id : null
      }

      const newProject = {
        ...prev,
        images: newImages,
        currentImageId: newCurrentImageId,
      }

      // 通知插件管理器图片变化
      const currentImage = newCurrentImageId
        ? newImages.find((img) => img.id === newCurrentImageId) || null
        : null
      newProject.pluginManager.setCurrentImage(currentImage)

      return newProject
    })
  }, [])

  const selectImage = useCallback((imageId: string) => {
    setProject((prev) => {
      const selectedImage = prev.images.find((img) => img.id === imageId)
      if (!selectedImage) return prev

      const newProject = {
        ...prev,
        currentImageId: imageId,
      }

      // 通知插件管理器图片变化
      newProject.pluginManager.setCurrentImage(selectedImage)

      return newProject
    })
  }, [])

  // 清除所有结果
  const clearAllResults = useCallback(
    (imageId: string) => {
      // 清除插件管理器中的所有结果
      const allResults = project.pluginManager.getAllResults()
      allResults.forEach((result) => {
        project.pluginManager.getPlugin(result.pluginId)?.clearResult?.()
      })
    },
    [project.pluginManager]
  )

  // 重置项目
  const resetProject = useCallback(() => {
    setProject((prev) => {
      // 销毁当前插件管理器
      prev.pluginManager.destroy()

      // 创建新的插件管理器
      const newPluginManager = new PluginManager()

      return {
        images: [],
        currentImageId: null,
        pluginManager: newPluginManager,
      }
    })
  }, [])

  // 兼容性方法（为了保持与原有代码的兼容性）
  const getMaskState = useCallback(() => {
    // 这里需要从CanvasEditor获取mask状态
    // 暂时返回undefined，实际实现时需要通过ref获取
    return undefined
  }, [])

  const getHistoryState = useCallback(() => {
    // 这里需要从CanvasEditor获取历史状态
    // 暂时返回undefined，实际实现时需要通过ref获取
    return undefined
  }, [])

  const saveMaskState = useCallback((imageId: string, maskState: any) => {
    // 这里需要保存mask状态到插件系统
    // 可以通过插件管理器的缓存系统实现
    console.log('Save mask state:', imageId, maskState)
  }, [])

  const saveHistoryState = useCallback(
    (imageId: string, history: string[], historyIndex: number) => {
      // 这里需要保存历史状态到插件系统
      console.log('Save history state:', imageId, history, historyIndex)
    },
    []
  )

  // 获取处理状态（兼容性）
  const getProcessingState = useCallback(
    (pluginId?: string): boolean => {
      if (pluginId) {
        // 通过插件管理器的内部方法获取处理状态
        return (
          (project.pluginManager as any).processingStates?.get(pluginId) ||
          false
        )
      }

      // 检查是否有任何插件在处理中
      const processingStates = (project.pluginManager as any).processingStates
      if (processingStates) {
        return Array.from(processingStates.values()).some(
          (state) => state === true
        )
      }

      return false
    },
    [project.pluginManager]
  )

  return {
    project,

    // Core functions
    getCurrentImage,
    getCurrentResult,
    getCurrentFinalResult,

    // Image management
    addImage,
    addImages,
    removeImage,
    selectImage,

    // Results management
    clearAllResults,
    resetProject,

    // Compatibility functions
    getCurrentMaskState: getMaskState,
    getCurrentHistoryState: getHistoryState,
    saveMaskState,
    saveHistoryState,
    getCurrentProcessingState: () => getProcessingState(),
    getCurrentBackgroundProcessingState: () =>
      getProcessingState('background-removal'),
    getCurrentBackgroundBlurProcessingState: () =>
      getProcessingState('background-blur'),

    // Legacy getters (for compatibility)
    getCurrentProcessedUrl: () => getCurrentResult()?.imageUrl || null,
    getCurrentBackgroundRemovedUrl: () => {
      const results = project.pluginManager.getAllResults()
      const bgResult = results.find((r) => r.pluginId === 'background-removal')
      return bgResult?.imageUrl || null
    },
    getCurrentBackgroundBlurredUrl: () => {
      const results = project.pluginManager.getAllResults()
      const blurResult = results.find((r) => r.pluginId === 'background-blur')
      return blurResult?.imageUrl || null
    },

    // Plugin manager access
    pluginManager: project.pluginManager,
  }
}
