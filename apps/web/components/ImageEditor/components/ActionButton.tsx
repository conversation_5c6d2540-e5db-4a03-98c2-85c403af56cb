// 动作按钮组件
'use client'

import React, { useState } from 'react'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Loader2, ChevronDown, ChevronUp } from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu'
import { ActionOptionsPanel } from './ActionOptionsPanel'
import type { PluginAction, IPlugin, ActionOption } from '../plugins/types'

interface ActionButtonProps {
  action: PluginAction
  plugin: IPlugin
  isProcessing: boolean
  isAvailable: boolean
  disabled: boolean
  onExecute: (options?: any) => Promise<void>
}

export const ActionButton: React.FC<ActionButtonProps> = ({
  action,
  plugin,
  isProcessing,
  isAvailable,
  disabled,
  onExecute
}) => {
  const [showOptions, setShowOptions] = useState(false)
  const [options, setOptions] = useState<Record<string, any>>({})

  // 获取动作选项
  const actionOptions = action.getOptions?.() || []
  const hasOptions = actionOptions.length > 0

  // 初始化默认选项值
  React.useEffect(() => {
    const defaultOptions: Record<string, any> = {}
    actionOptions.forEach(option => {
      defaultOptions[option.key] = option.defaultValue
    })
    setOptions(defaultOptions)
  }, [actionOptions])

  // 执行动作
  const handleExecute = async (executeOptions?: any) => {
    try {
      await onExecute(executeOptions || options)
    } catch (error) {
      console.error(`Failed to execute action ${action.id}:`, error)
    }
  }

  // 快速执行（使用默认选项）
  const handleQuickExecute = () => {
    if (hasOptions) {
      setShowOptions(true)
    } else {
      handleExecute()
    }
  }

  const Icon = action.icon

  return (
    <div className="space-y-2">
      <div className="flex items-center gap-2">
        {/* 主按钮 */}
        <Button
          onClick={handleQuickExecute}
          disabled={disabled || !isAvailable}
          className={`flex-1 justify-start ${
            isAvailable 
              ? 'bg-blue-600 hover:bg-blue-700 text-white' 
              : 'bg-gray-100 text-gray-400 cursor-not-allowed'
          }`}
        >
          {isProcessing ? (
            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
          ) : (
            Icon && <Icon className="w-4 h-4 mr-2" />
          )}
          <span className="flex-1 text-left">{action.name}</span>
          {!isAvailable && (
            <Badge variant="secondary" className="ml-2 text-xs">
              不可用
            </Badge>
          )}
        </Button>

        {/* 选项按钮 */}
        {hasOptions && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowOptions(!showOptions)}
            disabled={disabled || !isAvailable}
            className="border-gray-300"
          >
            {showOptions ? (
              <ChevronUp className="w-4 h-4" />
            ) : (
              <ChevronDown className="w-4 h-4" />
            )}
          </Button>
        )}

        {/* 更多操作菜单 */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              disabled={disabled || !isAvailable}
              className="border-gray-300"
            >
              ⋯
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => handleExecute()}>
              执行动作
            </DropdownMenuItem>
            {hasOptions && (
              <DropdownMenuItem onClick={() => setShowOptions(true)}>
                配置选项
              </DropdownMenuItem>
            )}
            <DropdownMenuItem 
              onClick={() => {
                // 重置为默认选项
                const defaultOptions: Record<string, any> = {}
                actionOptions.forEach(option => {
                  defaultOptions[option.key] = option.defaultValue
                })
                setOptions(defaultOptions)
              }}
            >
              重置选项
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* 动作描述 */}
      {action.description && (
        <p className="text-sm text-gray-600 px-2">
          {action.description}
        </p>
      )}

      {/* 插件信息 */}
      <div className="flex items-center justify-between px-2 text-xs text-gray-500">
        <span>来自: {plugin.name}</span>
        <Badge variant="outline" className="text-xs">
          {plugin.version}
        </Badge>
      </div>

      {/* 选项面板 */}
      {showOptions && hasOptions && (
        <ActionOptionsPanel
          options={actionOptions}
          values={options}
          onChange={setOptions}
          onExecute={handleExecute}
          onCancel={() => setShowOptions(false)}
          disabled={disabled || !isAvailable}
          isProcessing={isProcessing}
        />
      )}
    </div>
  )
}
