// 下载面板组件
'use client'

import React, { useState, useMemo } from 'react'
import { Button } from '../ui/button'
import { Label } from '../ui/label'
import { Separator } from '../ui/separator'
import { Badge } from '../ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select'
import { Download, Image, FileImage, Maximize2 } from 'lucide-react'
import { calculateQualityDimensions } from '../lib/image-utils'
import { downloadFile, generateFileName } from '../lib/download-utils'
import type { IPluginManager } from '../plugins/types'

interface DownloadPanelProps {
  pluginManager: IPluginManager
  currentImageDimensions?: {
    width: number
    height: number
  }
  disabled?: boolean
}

type QualityOption = 'preview' | 'high' | 'max'

const qualityOptions = [
  { value: 'preview' as QualityOption, label: '预览质量', description: '适合快速预览' },
  { value: 'high' as QualityOption, label: '高质量', description: '适合一般使用' },
  { value: 'max' as QualityOption, label: '最高质量', description: '适合打印或专业用途' }
]

export const DownloadPanel: React.FC<DownloadPanelProps> = ({
  pluginManager,
  currentImageDimensions,
  disabled = false
}) => {
  const [selectedQuality, setSelectedQuality] = useState<QualityOption>('high')
  const [isDownloading, setIsDownloading] = useState(false)

  // 获取最新的处理结果
  const latestResult = useMemo(() => {
    return pluginManager.getLatestResult()
  }, [pluginManager])

  // 计算不同质量的尺寸
  const dimensions = useMemo(() => {
    return calculateQualityDimensions(currentImageDimensions)
  }, [currentImageDimensions])

  // 获取当前选择质量的尺寸信息
  const currentDimensions = dimensions[selectedQuality]

  // 检查是否有可下载的内容
  const hasDownloadableContent = latestResult?.imageUrl

  // 下载处理
  const handleDownload = async () => {
    if (!hasDownloadableContent || !latestResult) {
      return
    }

    setIsDownloading(true)

    try {
      // 生成文件名
      const fileName = generateFileName(
        latestResult.pluginId,
        latestResult.actionId,
        selectedQuality
      )

      // 如果需要调整尺寸
      let downloadUrl = latestResult.imageUrl

      if (selectedQuality !== 'max' && currentDimensions) {
        // 创建canvas调整尺寸
        downloadUrl = await resizeImage(
          latestResult.imageUrl,
          currentDimensions.width,
          currentDimensions.height
        )
      }

      // 下载文件
      await downloadFile(downloadUrl, fileName)

    } catch (error) {
      console.error('Download failed:', error)
      // 这里可以显示错误提示
    } finally {
      setIsDownloading(false)
    }
  }

  // 调整图片尺寸
  const resizeImage = async (
    imageUrl: string,
    targetWidth: number,
    targetHeight: number
  ): Promise<string> => {
    return new Promise((resolve, reject) => {
      const img = new Image()
      img.crossOrigin = 'anonymous'
      
      img.onload = () => {
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')
        
        if (!ctx) {
          reject(new Error('Failed to get canvas context'))
          return
        }

        canvas.width = targetWidth
        canvas.height = targetHeight

        // 使用高质量缩放
        ctx.imageSmoothingEnabled = true
        ctx.imageSmoothingQuality = 'high'
        
        ctx.drawImage(img, 0, 0, targetWidth, targetHeight)
        
        resolve(canvas.toDataURL('image/png', 1.0))
      }

      img.onerror = () => reject(new Error('Failed to load image'))
      img.src = imageUrl
    })
  }

  return (
    <div className="space-y-4">
      {/* 当前结果信息 */}
      {latestResult ? (
        <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <Image className="w-4 h-4 text-blue-600" />
            <span className="font-medium text-blue-800">当前结果</span>
          </div>
          <div className="text-sm text-blue-700 space-y-1">
            <p>插件: {latestResult.pluginId}</p>
            <p>操作: {latestResult.actionId}</p>
            <p>时间: {new Date(latestResult.timestamp).toLocaleString()}</p>
          </div>
        </div>
      ) : (
        <div className="p-3 bg-gray-50 border border-gray-200 rounded-lg text-center">
          <FileImage className="w-8 h-8 text-gray-400 mx-auto mb-2" />
          <p className="text-sm text-gray-600">暂无可下载的内容</p>
          <p className="text-xs text-gray-500 mt-1">
            请先处理图片以生成结果
          </p>
        </div>
      )}

      <Separator />

      {/* 质量选择 */}
      <div className="space-y-3">
        <Label className="text-sm font-medium">下载质量</Label>
        
        <Select
          value={selectedQuality}
          onValueChange={(value: QualityOption) => setSelectedQuality(value)}
          disabled={disabled || !hasDownloadableContent}
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {qualityOptions.map(option => (
              <SelectItem key={option.value} value={option.value}>
                <div className="flex flex-col">
                  <span>{option.label}</span>
                  <span className="text-xs text-gray-500">
                    {option.description}
                  </span>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* 尺寸信息 */}
        {currentDimensions && (
          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>输出尺寸:</span>
            <Badge variant="outline">
              {currentDimensions.width} × {currentDimensions.height}
            </Badge>
          </div>
        )}
      </div>

      <Separator />

      {/* 下载按钮 */}
      <div className="space-y-2">
        <Button
          onClick={handleDownload}
          disabled={disabled || !hasDownloadableContent || isDownloading}
          className="w-full bg-green-600 hover:bg-green-700 text-white"
        >
          {isDownloading ? (
            <>
              <div className="w-4 h-4 mr-2 animate-spin rounded-full border-2 border-white border-t-transparent" />
              下载中...
            </>
          ) : (
            <>
              <Download className="w-4 h-4 mr-2" />
              下载图片
            </>
          )}
        </Button>

        {/* 快速下载选项 */}
        {hasDownloadableContent && (
          <div className="grid grid-cols-2 gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setSelectedQuality('preview')
                setTimeout(handleDownload, 100)
              }}
              disabled={disabled || isDownloading}
              className="text-xs"
            >
              <Download className="w-3 h-3 mr-1" />
              快速下载
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setSelectedQuality('max')
                setTimeout(handleDownload, 100)
              }}
              disabled={disabled || isDownloading}
              className="text-xs"
            >
              <Maximize2 className="w-3 h-3 mr-1" />
              最高质量
            </Button>
          </div>
        )}
      </div>

      {/* 提示信息 */}
      <div className="text-xs text-gray-500 space-y-1">
        <p>• 预览质量适合快速查看效果</p>
        <p>• 高质量适合日常使用和分享</p>
        <p>• 最高质量适合打印和专业用途</p>
      </div>
    </div>
  )
}
