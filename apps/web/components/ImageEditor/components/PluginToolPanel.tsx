// 插件化工具面板
'use client'

import React, { useState, useEffect, useMemo } from 'react'
import { Card } from '../ui/card'
import { Button } from '../ui/button'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '../ui/tabs'
import { Badge } from '../ui/badge'
import { Separator } from '../ui/separator'
import {
  Download,
  HelpCircle,
  Settings,
  Zap,
  Layers,
  Palette,
  Image,
  Sparkles
} from 'lucide-react'
import type { IPluginManager, PluginAction, ToolbarItem } from '../plugins/types'
import { ActionButton } from './ActionButton'
import { PluginSettings } from './PluginSettings'
import { DownloadPanel } from './DownloadPanel'

interface PluginToolPanelProps {
  pluginManager: IPluginManager
  disabled?: boolean
  onShowHelp?: () => void
  currentImageDimensions?: {
    width: number
    height: number
  }
  getMaskCanvas?: () => HTMLCanvasElement | null
  originalImageUrl?: string
}

// 类别图标映射
const categoryIcons = {
  background: Palette,
  object: Layers,
  enhancement: Sparkles,
  ai: Zap,
  custom: Settings
}

// 类别名称映射
const categoryNames = {
  background: '背景处理',
  object: '物体编辑',
  enhancement: '图像增强',
  ai: 'AI功能',
  custom: '自定义'
}

export const PluginToolPanel: React.FC<PluginToolPanelProps> = ({
  pluginManager,
  disabled = false,
  onShowHelp,
  currentImageDimensions,
  getMaskCanvas,
  originalImageUrl
}) => {
  const [activeTab, setActiveTab] = useState('actions')
  const [selectedCategory, setSelectedCategory] = useState<string>('background')
  const [processingStates, setProcessingStates] = useState<Record<string, boolean>>({})
  const [showSettings, setShowSettings] = useState(false)

  // 获取所有启用的插件
  const enabledPlugins = useMemo(() => {
    return pluginManager.getEnabledPlugins()
  }, [pluginManager])

  // 按类别分组的动作
  const actionsByCategory = useMemo(() => {
    const grouped: Record<string, PluginAction[]> = {}
    
    enabledPlugins.forEach(plugin => {
      const actions = plugin.getActions()
      actions.forEach(action => {
        if (!grouped[action.category]) {
          grouped[action.category] = []
        }
        grouped[action.category].push(action)
      })
    })
    
    return grouped
  }, [enabledPlugins])

  // 获取工具栏项
  const toolbarItems = useMemo(() => {
    const items: ToolbarItem[] = []
    
    enabledPlugins.forEach(plugin => {
      const pluginItems = plugin.getToolbarItems?.() || []
      items.push(...pluginItems)
    })
    
    // 按位置和顺序排序
    return items.sort((a, b) => a.order - b.order)
  }, [enabledPlugins])

  // 监听处理状态变化
  useEffect(() => {
    const unsubscribe = pluginManager.on('processing:state', (data) => {
      setProcessingStates(prev => ({
        ...prev,
        [data.pluginId]: data.isProcessing
      }))
    })

    return unsubscribe
  }, [pluginManager])

  // 执行动作
  const handleActionExecute = async (pluginId: string, actionId: string, options?: any) => {
    try {
      await pluginManager.executeAction(pluginId, actionId, options)
    } catch (error) {
      console.error(`Failed to execute action ${actionId}:`, error)
    }
  }

  // 检查动作是否可用
  const isActionAvailable = (action: PluginAction): boolean => {
    const plugin = enabledPlugins.find(p => 
      p.getActions().some(a => a.id === action.id)
    )
    
    if (!plugin) return false
    
    const context = (pluginManager as any).createPluginContext?.(plugin.id)
    return action.isAvailable ? action.isAvailable(context) : true
  }

  // 获取可用的类别
  const availableCategories = Object.keys(actionsByCategory).filter(
    category => actionsByCategory[category].length > 0
  )

  return (
    <Card className="w-80 h-full flex flex-col bg-white shadow-lg">
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-800">工具面板</h3>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={onShowHelp}
              className="border-gray-300 text-gray-700 hover:bg-gray-50"
            >
              <HelpCircle className="w-4 h-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowSettings(true)}
              className="border-gray-300 text-gray-700 hover:bg-gray-50"
            >
              <Settings className="w-4 h-4" />
            </Button>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="actions">动作</TabsTrigger>
            <TabsTrigger value="tools">工具</TabsTrigger>
            <TabsTrigger value="download">下载</TabsTrigger>
          </TabsList>

          <TabsContent value="actions" className="mt-4 space-y-4">
            {/* 类别选择 */}
            <div className="flex flex-wrap gap-2">
              {availableCategories.map(category => {
                const Icon = categoryIcons[category as keyof typeof categoryIcons] || Settings
                const isSelected = selectedCategory === category
                
                return (
                  <Button
                    key={category}
                    variant={isSelected ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedCategory(category)}
                    className={`flex items-center gap-2 ${
                      isSelected 
                        ? 'bg-blue-600 text-white' 
                        : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    {categoryNames[category as keyof typeof categoryNames] || category}
                    <Badge variant="secondary" className="ml-1">
                      {actionsByCategory[category].length}
                    </Badge>
                  </Button>
                )
              })}
            </div>

            <Separator />

            {/* 动作列表 */}
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {actionsByCategory[selectedCategory]?.map(action => {
                const plugin = enabledPlugins.find(p => 
                  p.getActions().some(a => a.id === action.id)
                )
                
                if (!plugin) return null

                const isProcessing = processingStates[plugin.id] || false
                const isAvailable = isActionAvailable(action)

                return (
                  <ActionButton
                    key={`${plugin.id}-${action.id}`}
                    action={action}
                    plugin={plugin}
                    isProcessing={isProcessing}
                    isAvailable={isAvailable}
                    disabled={disabled || isProcessing}
                    onExecute={(options) => handleActionExecute(plugin.id, action.id, options)}
                  />
                )
              })}
            </div>
          </TabsContent>

          <TabsContent value="tools" className="mt-4 space-y-4">
            {/* 工具栏项 */}
            <div className="space-y-4 max-h-96 overflow-y-auto">
              {toolbarItems.map(item => {
                const plugin = enabledPlugins.find(p => 
                  p.getToolbarItems?.().some(t => t.id === item.id)
                )
                
                if (!plugin) return null

                const context = (pluginManager as any).createPluginContext?.(plugin.id)
                const Component = item.component

                return (
                  <div key={item.id} className="p-3 border border-gray-200 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      {item.icon && <item.icon className="w-4 h-4" />}
                      <span className="font-medium">{item.name}</span>
                    </div>
                    <Component
                      context={context}
                      disabled={disabled}
                    />
                  </div>
                )
              })}
              
              {toolbarItems.length === 0 && (
                <div className="text-center text-gray-500 py-8">
                  当前没有可用的工具
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="download" className="mt-4">
            <DownloadPanel
              pluginManager={pluginManager}
              currentImageDimensions={currentImageDimensions}
              disabled={disabled}
            />
          </TabsContent>
        </Tabs>
      </div>

      {/* 插件设置对话框 */}
      {showSettings && (
        <PluginSettings
          pluginManager={pluginManager}
          isOpen={showSettings}
          onClose={() => setShowSettings(false)}
        />
      )}
    </Card>
  )
}
