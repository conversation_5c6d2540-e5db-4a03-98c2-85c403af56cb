// 插件设置组件
'use client'

import React, { useState, useEffect } from 'react'
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from '../ui/dialog'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import { Switch } from '../ui/switch'
import { Separator } from '../ui/separator'
import { Badge } from '../ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Settings, Info, Trash2, RotateCcw } from 'lucide-react'
import type { IPluginManager, IPlugin, PluginConfig } from '../plugins/types'

interface PluginSettingsProps {
  pluginManager: IPluginManager
  isOpen: boolean
  onClose: () => void
}

export const PluginSettings: React.FC<PluginSettingsProps> = ({
  pluginManager,
  isOpen,
  onClose
}) => {
  const [plugins, setPlugins] = useState<IPlugin[]>([])
  const [configs, setConfigs] = useState<Record<string, PluginConfig>>({})
  const [activeTab, setActiveTab] = useState('plugins')

  // 加载插件和配置
  useEffect(() => {
    if (isOpen) {
      const allPlugins = pluginManager.getAllPlugins()
      setPlugins(allPlugins)

      const allConfigs: Record<string, PluginConfig> = {}
      allPlugins.forEach(plugin => {
        const config = pluginManager.getPluginConfig(plugin.id)
        if (config) {
          allConfigs[plugin.id] = config
        }
      })
      setConfigs(allConfigs)
    }
  }, [isOpen, pluginManager])

  // 更新插件配置
  const updatePluginConfig = (pluginId: string, updates: Partial<PluginConfig>) => {
    const newConfig = { ...configs[pluginId], ...updates }
    setConfigs(prev => ({ ...prev, [pluginId]: newConfig }))
    pluginManager.setPluginConfig(pluginId, updates)
  }

  // 重置插件配置
  const resetPluginConfig = (pluginId: string) => {
    const defaultConfig: PluginConfig = {
      enabled: true,
      settings: {},
      order: 0
    }
    setConfigs(prev => ({ ...prev, [pluginId]: defaultConfig }))
    pluginManager.setPluginConfig(pluginId, defaultConfig)
  }

  // 清除插件缓存
  const clearPluginCache = async (pluginId: string) => {
    try {
      // 通过插件管理器清除缓存
      const plugin = pluginManager.getPlugin(pluginId)
      if (plugin) {
        // 发送清除缓存事件
        pluginManager.emit('cache:clear', { pluginId })
      }
    } catch (error) {
      console.error(`Failed to clear cache for plugin ${pluginId}:`, error)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="w-5 h-5" />
            插件设置
          </DialogTitle>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="plugins">插件管理</TabsTrigger>
            <TabsTrigger value="settings">全局设置</TabsTrigger>
            <TabsTrigger value="about">关于</TabsTrigger>
          </TabsList>

          <TabsContent value="plugins" className="space-y-4 max-h-96 overflow-y-auto">
            {plugins.map(plugin => {
              const config = configs[plugin.id]
              if (!config) return null

              const Icon = plugin.icon

              return (
                <Card key={plugin.id}>
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        {Icon && <Icon className="w-6 h-6 text-blue-600" />}
                        <div>
                          <CardTitle className="text-lg">{plugin.name}</CardTitle>
                          <CardDescription>{plugin.description}</CardDescription>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">{plugin.version}</Badge>
                        <Switch
                          checked={config.enabled}
                          onCheckedChange={(enabled) => 
                            updatePluginConfig(plugin.id, { enabled })
                          }
                        />
                      </div>
                    </div>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    {/* 插件信息 */}
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <Label className="text-gray-600">作者</Label>
                        <p>{plugin.author || '未知'}</p>
                      </div>
                      <div>
                        <Label className="text-gray-600">版本</Label>
                        <p>{plugin.version}</p>
                      </div>
                    </div>

                    {/* 插件动作 */}
                    <div>
                      <Label className="text-gray-600">提供的功能</Label>
                      <div className="flex flex-wrap gap-2 mt-1">
                        {plugin.getActions().map(action => (
                          <Badge key={action.id} variant="secondary">
                            {action.name}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    {/* 插件设置 */}
                    <div className="space-y-3">
                      <Label className="text-gray-600">设置</Label>
                      
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor={`order-${plugin.id}`} className="text-sm">
                            显示顺序
                          </Label>
                          <Input
                            id={`order-${plugin.id}`}
                            type="number"
                            value={config.order}
                            onChange={(e) => 
                              updatePluginConfig(plugin.id, { 
                                order: parseInt(e.target.value) || 0 
                              })
                            }
                            className="mt-1"
                          />
                        </div>
                      </div>

                      {/* 插件特定设置面板 */}
                      {plugin.getSettingsPanel && (
                        <div className="border border-gray-200 rounded-lg p-3">
                          <Label className="text-sm font-medium">插件设置</Label>
                          {React.createElement(plugin.getSettingsPanel(), {
                            context: (pluginManager as any).createPluginContext?.(plugin.id),
                            onSettingsChange: (settings: Record<string, any>) => {
                              updatePluginConfig(plugin.id, { settings })
                            }
                          })}
                        </div>
                      )}
                    </div>

                    {/* 操作按钮 */}
                    <div className="flex gap-2 pt-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => resetPluginConfig(plugin.id)}
                        className="flex items-center gap-2"
                      >
                        <RotateCcw className="w-4 h-4" />
                        重置配置
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => clearPluginCache(plugin.id)}
                        className="flex items-center gap-2"
                      >
                        <Trash2 className="w-4 h-4" />
                        清除缓存
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </TabsContent>

          <TabsContent value="settings" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>全局设置</CardTitle>
                <CardDescription>
                  这些设置会影响所有插件的行为
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label>缓存策略</Label>
                  <p className="text-sm text-gray-600">
                    插件系统会自动缓存处理结果以提高性能
                  </p>
                </div>
                
                <Separator />
                
                <div className="space-y-2">
                  <Label>错误处理</Label>
                  <p className="text-sm text-gray-600">
                    当插件执行失败时，系统会显示错误信息并尝试恢复
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="about" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Info className="w-5 h-5" />
                  关于插件系统
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-sm text-gray-600">
                  这是一个可扩展的图片编辑插件系统，支持动态加载和管理各种图片处理功能。
                </p>
                
                <div className="space-y-2">
                  <h4 className="font-medium">系统特性</h4>
                  <ul className="text-sm text-gray-600 space-y-1 list-disc list-inside">
                    <li>插件化架构，易于扩展</li>
                    <li>智能缓存机制，提高性能</li>
                    <li>事件驱动的插件通信</li>
                    <li>统一的用户界面</li>
                    <li>灵活的配置管理</li>
                  </ul>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium">已加载插件</h4>
                  <p className="text-sm text-gray-600">
                    当前系统已加载 {plugins.length} 个插件，
                    其中 {plugins.filter(p => configs[p.id]?.enabled).length} 个已启用。
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <div className="flex justify-end pt-4">
          <Button onClick={onClose}>
            关闭
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
