// 简化版Checkbox组件
'use client'

import * as React from 'react'
import { Check } from 'lucide-react'

interface CheckboxProps {
  checked?: boolean
  onCheckedChange?: (checked: boolean) => void
  disabled?: boolean
  className?: string
  id?: string
}

const Checkbox = React.forwardRef<HTMLInputElement, CheckboxProps>(
  (
    { className = '', checked, onCheckedChange, disabled, id, ...props },
    ref
  ) => {
    return (
      <div className="relative inline-flex items-center">
        <input
          ref={ref}
          type="checkbox"
          id={id}
          checked={checked}
          onChange={(e) => onCheckedChange?.(e.target.checked)}
          disabled={disabled}
          className="sr-only"
          {...props}
        />
        <div
          className={`
            h-4 w-4 shrink-0 rounded-sm border border-gray-300
            flex items-center justify-center cursor-pointer
            ${checked ? 'bg-blue-600 border-blue-600 text-white' : 'bg-white'}
            ${
              disabled
                ? 'opacity-50 cursor-not-allowed'
                : 'hover:border-blue-400'
            }
            ${className}
          `}
          onClick={() => !disabled && onCheckedChange?.(!checked)}
        >
          {checked && <Check className="h-3 w-3" />}
        </div>
      </div>
    )
  }
)

Checkbox.displayName = 'Checkbox'

export { Checkbox }
