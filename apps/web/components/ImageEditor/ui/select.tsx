// 简化版Select组件
'use client'

import * as React from 'react'
import { Check, ChevronDown } from 'lucide-react'

// 简化版Select组件 - 使用原生select元素
interface SelectProps {
  value?: string
  onValueChange?: (value: string) => void
  disabled?: boolean
  className?: string
  children?: React.ReactNode
}

interface SelectTriggerProps {
  className?: string
  children?: React.ReactNode
}

interface SelectContentProps {
  className?: string
  children?: React.ReactNode
}

interface SelectItemProps {
  value: string
  className?: string
  children?: React.ReactNode
}

interface SelectValueProps {
  placeholder?: string
}

// 使用原生select的简化实现
const Select: React.FC<SelectProps> = ({
  value,
  onValueChange,
  disabled,
  children,
}) => {
  const selectRef = React.useRef<HTMLSelectElement>(null)

  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    onValueChange?.(e.target.value)
  }

  return (
    <div className="relative">
      <select
        ref={selectRef}
        value={value}
        onChange={handleChange}
        disabled={disabled}
        className="flex h-10 w-full items-center justify-between rounded-md border border-gray-300 bg-white px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 appearance-none"
      >
        {children}
      </select>
      <ChevronDown className="absolute right-3 top-3 h-4 w-4 opacity-50 pointer-events-none" />
    </div>
  )
}

const SelectTrigger: React.FC<SelectTriggerProps> = ({ children }) => {
  return <>{children}</>
}

const SelectContent: React.FC<SelectContentProps> = ({ children }) => {
  return <>{children}</>
}

const SelectItem: React.FC<SelectItemProps> = ({ value, children }) => {
  return <option value={value}>{children}</option>
}

const SelectValue: React.FC<SelectValueProps> = ({ placeholder }) => {
  return (
    <option value="" disabled>
      {placeholder}
    </option>
  )
}

export { Select, SelectTrigger, SelectContent, SelectItem, SelectValue }
