// 简化版Switch组件
'use client'

import * as React from 'react'

interface SwitchProps {
  checked?: boolean
  onCheckedChange?: (checked: boolean) => void
  disabled?: boolean
  className?: string
  id?: string
}

const Switch = React.forwardRef<HTMLInputElement, SwitchProps>(
  (
    { className = '', checked, onCheckedChange, disabled, id, ...props },
    ref
  ) => {
    return (
      <div className="relative inline-flex items-center">
        <input
          ref={ref}
          type="checkbox"
          id={id}
          checked={checked}
          onChange={(e) => onCheckedChange?.(e.target.checked)}
          disabled={disabled}
          className="sr-only"
          {...props}
        />
        <div
          className={`
            inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full
            border-2 border-transparent transition-colors
            ${checked ? 'bg-blue-600' : 'bg-gray-200'}
            ${
              disabled ? 'opacity-50 cursor-not-allowed' : 'hover:bg-opacity-80'
            }
            ${className}
          `}
          onClick={() => !disabled && onCheckedChange?.(!checked)}
        >
          <div
            className={`
              pointer-events-none block h-5 w-5 rounded-full bg-white shadow-lg
              ring-0 transition-transform
              ${checked ? 'translate-x-5' : 'translate-x-0'}
            `}
          />
        </div>
      </div>
    )
  }
)

Switch.displayName = 'Switch'

export { Switch }
