# 插件系统测试指南

## 快速开始

### 1. 启用插件系统

在项目根目录创建 `.env.local` 文件：

```bash
# 启用插件系统
NEXT_PUBLIC_USE_PLUGIN_SYSTEM=true
```

### 2. 启动开发服务器

```bash
npm run dev
```

### 3. 测试方式

#### 方式一：专用测试页面
访问 `http://localhost:3000/test-plugins` 查看插件系统测试页面，可以：
- 查看所有已注册的插件
- 测试插件注册功能
- 测试事件系统
- 测试模拟图片处理

#### 方式二：主编辑器页面
访问 `http://localhost:3000` 使用插件化的图片编辑器：
- 上传图片
- 使用各种插件功能
- 体验新的插件化界面

## 功能对比测试

### 原有功能 vs 插件化功能

| 功能 | 原有实现 | 插件化实现 | 测试方法 |
|------|----------|------------|----------|
| 背景移除 | 集成在ToolPanel | BackgroundRemovalPlugin | 上传图片 → 点击"移除背景" |
| 背景模糊 | 复杂状态管理 | BackgroundBlurPlugin | 移除背景后 → 点击"模糊背景" |
| 背景替换 | 分散逻辑 | BackgroundReplacementPlugin | 移除背景后 → 选择背景替换 |
| 物体移除 | 紧耦合实现 | ObjectRemovalPlugin | 画mask → 点击"移除物体" |

### 新增功能

| 功能 | 插件 | 测试方法 |
|------|------|----------|
| AI智能抠图 | AISegmentationPlugin | 上传图片 → AI功能 → 智能抠图 |
| AI智能替换 | AIReplacementPlugin | 上传图片 → AI功能 → 智能替换 |

## 测试检查清单

### ✅ 基础功能测试

- [ ] 插件系统初始化成功
- [ ] 所有内置插件正确注册
- [ ] 自定义插件正确注册
- [ ] 插件界面正确显示

### ✅ 图片处理测试

- [ ] 图片上传功能正常
- [ ] 背景移除功能正常
- [ ] 背景模糊功能正常
- [ ] 背景替换功能正常
- [ ] 物体移除功能正常

### ✅ 插件交互测试

- [ ] 插件间依赖关系正确
- [ ] 缓存机制工作正常
- [ ] 事件系统工作正常
- [ ] 错误处理正确

### ✅ UI/UX测试

- [ ] 插件工具面板显示正确
- [ ] 动作按钮功能正常
- [ ] 选项面板工作正常
- [ ] 进度指示正确

## 性能测试

### 内存使用
```javascript
// 在浏览器控制台中运行
console.log('内存使用:', performance.memory)
```

### 插件加载时间
```javascript
// 测试插件初始化时间
console.time('plugin-init')
// ... 插件初始化代码
console.timeEnd('plugin-init')
```

## 调试技巧

### 1. 启用详细日志
```javascript
// 在浏览器控制台中
localStorage.setItem('debug-plugins', 'true')
```

### 2. 查看插件状态
```javascript
// 获取插件管理器实例
const pluginManager = window.__PLUGIN_MANAGER__
console.log('所有插件:', pluginManager.getAllPlugins())
console.log('处理状态:', pluginManager.processingStates)
```

### 3. 监听插件事件
```javascript
// 监听所有插件事件
pluginManager.on('*', (event, data) => {
  console.log('插件事件:', event, data)
})
```

## 常见问题

### Q: 插件系统没有启用？
A: 检查 `.env.local` 文件中的 `NEXT_PUBLIC_USE_PLUGIN_SYSTEM=true`

### Q: 插件功能不工作？
A: 
1. 检查浏览器控制台错误
2. 确认插件正确注册
3. 检查插件依赖关系

### Q: 性能问题？
A: 
1. 检查缓存是否正常工作
2. 查看内存使用情况
3. 检查是否有内存泄漏

### Q: UI显示异常？
A: 
1. 检查CSS样式加载
2. 确认组件正确渲染
3. 查看React开发工具

## 报告问题

如果发现问题，请提供：
1. 浏览器和版本
2. 错误信息和堆栈跟踪
3. 重现步骤
4. 预期行为 vs 实际行为

## 贡献测试

欢迎添加更多测试用例：
1. 在 `test/` 目录下添加测试文件
2. 更新测试检查清单
3. 提交PR

---

更多信息请参考：
- [插件开发指南](./plugins/README.md)
- [架构文档](./REFACTOR_SUMMARY.md)
