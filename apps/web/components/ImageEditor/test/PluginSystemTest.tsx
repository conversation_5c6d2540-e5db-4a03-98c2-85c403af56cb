// 插件系统测试组件
'use client'

import React, { useState, useEffect } from 'react'
import { Button } from '../ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Separator } from '../ui/separator'
import { createPluginManager, allPlugins } from '../plugins'
import type { IPluginManager, ProcessingResult } from '../plugins/types'

export const PluginSystemTest: React.FC = () => {
  const [pluginManager, setPluginManager] = useState<IPluginManager | null>(null)
  const [isInitialized, setIsInitialized] = useState(false)
  const [testResults, setTestResults] = useState<string[]>([])
  const [isRunningTests, setIsRunningTests] = useState(false)

  // 初始化插件系统
  useEffect(() => {
    const initializePluginSystem = async () => {
      try {
        const manager = await createPluginManager()
        setPluginManager(manager)
        setIsInitialized(true)
        addTestResult('✅ 插件系统初始化成功')
      } catch (error) {
        addTestResult(`❌ 插件系统初始化失败: ${error}`)
      }
    }

    initializePluginSystem()

    return () => {
      if (pluginManager) {
        pluginManager.destroy()
      }
    }
  }, [])

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`])
  }

  // 测试插件注册
  const testPluginRegistration = async () => {
    if (!pluginManager) return

    try {
      const allPluginsList = pluginManager.getAllPlugins()
      addTestResult(`✅ 已注册 ${allPluginsList.length} 个插件`)
      
      allPluginsList.forEach(plugin => {
        addTestResult(`  - ${plugin.name} (${plugin.id}) v${plugin.version}`)
      })
    } catch (error) {
      addTestResult(`❌ 插件注册测试失败: ${error}`)
    }
  }

  // 测试插件动作
  const testPluginActions = async () => {
    if (!pluginManager) return

    try {
      const enabledPlugins = pluginManager.getEnabledPlugins()
      let totalActions = 0

      enabledPlugins.forEach(plugin => {
        const actions = plugin.getActions()
        totalActions += actions.length
        addTestResult(`✅ ${plugin.name} 提供 ${actions.length} 个动作`)
        
        actions.forEach(action => {
          addTestResult(`  - ${action.name} (${action.id}) - ${action.category}`)
        })
      })

      addTestResult(`✅ 总共 ${totalActions} 个可用动作`)
    } catch (error) {
      addTestResult(`❌ 插件动作测试失败: ${error}`)
    }
  }

  // 测试事件系统
  const testEventSystem = async () => {
    if (!pluginManager) return

    try {
      let eventReceived = false
      
      const unsubscribe = pluginManager.on('test-event', (data) => {
        eventReceived = true
        addTestResult(`✅ 事件系统测试成功，收到数据: ${JSON.stringify(data)}`)
      })

      pluginManager.emit('test-event', { message: 'Hello from test!' })

      setTimeout(() => {
        if (!eventReceived) {
          addTestResult('❌ 事件系统测试失败：未收到事件')
        }
        unsubscribe()
      }, 100)
    } catch (error) {
      addTestResult(`❌ 事件系统测试失败: ${error}`)
    }
  }

  // 测试模拟图片处理
  const testImageProcessing = async () => {
    if (!pluginManager) return

    try {
      // 创建模拟图片数据
      const mockImageData = {
        id: 'test-image',
        file: new File([''], 'test.jpg', { type: 'image/jpeg' }),
        url: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=',
        width: 800,
        height: 600,
        name: 'test.jpg'
      }

      // 设置当前图片
      pluginManager.setCurrentImage(mockImageData)
      addTestResult('✅ 设置模拟图片数据成功')

      // 测试背景移除插件（模拟）
      const bgRemovalPlugin = pluginManager.getPlugin('background-removal')
      if (bgRemovalPlugin) {
        addTestResult('✅ 找到背景移除插件')
        
        const actions = bgRemovalPlugin.getActions()
        const removeAction = actions.find(a => a.id === 'remove-background')
        
        if (removeAction) {
          addTestResult('✅ 找到背景移除动作')
          
          // 检查动作可用性
          const context = (pluginManager as any).createPluginContext('background-removal')
          const isAvailable = removeAction.isAvailable ? removeAction.isAvailable(context) : true
          
          if (isAvailable) {
            addTestResult('✅ 背景移除动作可用')
          } else {
            addTestResult('⚠️ 背景移除动作当前不可用')
          }
        }
      }
    } catch (error) {
      addTestResult(`❌ 图片处理测试失败: ${error}`)
    }
  }

  // 运行所有测试
  const runAllTests = async () => {
    if (!pluginManager || isRunningTests) return

    setIsRunningTests(true)
    setTestResults([])
    
    addTestResult('🚀 开始运行插件系统测试...')
    
    await testPluginRegistration()
    await new Promise(resolve => setTimeout(resolve, 500))
    
    await testPluginActions()
    await new Promise(resolve => setTimeout(resolve, 500))
    
    await testEventSystem()
    await new Promise(resolve => setTimeout(resolve, 500))
    
    await testImageProcessing()
    
    addTestResult('🎉 所有测试完成！')
    setIsRunningTests(false)
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>插件系统测试</CardTitle>
          <CardDescription>
            测试图片编辑器插件系统的各项功能
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4 mb-4">
            <Badge variant={isInitialized ? "default" : "secondary"}>
              {isInitialized ? "已初始化" : "未初始化"}
            </Badge>
            {isInitialized && (
              <Badge variant="outline">
                {pluginManager?.getAllPlugins().length || 0} 个插件
              </Badge>
            )}
          </div>

          <div className="space-y-2">
            <Button 
              onClick={runAllTests} 
              disabled={!isInitialized || isRunningTests}
              className="w-full"
            >
              {isRunningTests ? "运行中..." : "运行所有测试"}
            </Button>
            
            <div className="grid grid-cols-2 gap-2">
              <Button 
                variant="outline" 
                onClick={testPluginRegistration}
                disabled={!isInitialized || isRunningTests}
              >
                测试插件注册
              </Button>
              <Button 
                variant="outline" 
                onClick={testPluginActions}
                disabled={!isInitialized || isRunningTests}
              >
                测试插件动作
              </Button>
              <Button 
                variant="outline" 
                onClick={testEventSystem}
                disabled={!isInitialized || isRunningTests}
              >
                测试事件系统
              </Button>
              <Button 
                variant="outline" 
                onClick={testImageProcessing}
                disabled={!isInitialized || isRunningTests}
              >
                测试图片处理
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>测试结果</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-gray-50 p-4 rounded-lg max-h-96 overflow-y-auto">
            {testResults.length === 0 ? (
              <p className="text-gray-500">点击上方按钮开始测试...</p>
            ) : (
              <div className="space-y-1">
                {testResults.map((result, index) => (
                  <div key={index} className="text-sm font-mono">
                    {result}
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {isInitialized && (
        <Card>
          <CardHeader>
            <CardTitle>已注册插件</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {pluginManager?.getAllPlugins().map(plugin => (
                <div key={plugin.id} className="border rounded-lg p-3">
                  <div className="flex items-center gap-2 mb-2">
                    {plugin.icon && <plugin.icon className="w-4 h-4" />}
                    <h4 className="font-medium">{plugin.name}</h4>
                    <Badge variant="outline" className="text-xs">
                      v{plugin.version}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">
                    {plugin.description}
                  </p>
                  <div className="flex flex-wrap gap-1">
                    {plugin.getActions().map(action => (
                      <Badge key={action.id} variant="secondary" className="text-xs">
                        {action.name}
                      </Badge>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
