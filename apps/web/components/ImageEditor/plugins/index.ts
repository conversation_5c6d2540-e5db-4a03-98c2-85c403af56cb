// 插件系统主入口
export * from './types'
export * from './EventBus'
export * from './PluginManager'
export * from './BasePlugin'

// 内置插件
export * from './builtin'

// 自定义插件
export * from './custom'

// 插件管理器和相关工具
import { PluginManager } from './PluginManager'
import { builtinPlugins, defaultPluginConfigs } from './builtin'
import { customPlugins, customPluginConfigs } from './custom'
import type { IPlugin, PluginConfig } from './types'

// 所有可用插件
export const allPlugins: IPlugin[] = [
  ...builtinPlugins,
  ...customPlugins
]

// 所有插件的默认配置
export const allPluginConfigs = {
  ...defaultPluginConfigs,
  ...customPluginConfigs
}

// 创建并初始化插件管理器的工厂函数
export async function createPluginManager(
  enabledPluginIds?: string[],
  customConfigs?: Record<string, Partial<PluginConfig>>
): Promise<PluginManager> {
  const manager = new PluginManager()

  // 注册插件
  for (const plugin of allPlugins) {
    // 检查是否启用此插件
    if (enabledPluginIds && !enabledPluginIds.includes(plugin.id)) {
      continue
    }

    // 获取配置
    const defaultConfig = allPluginConfigs[plugin.id as keyof typeof allPluginConfigs]
    const customConfig = customConfigs?.[plugin.id]
    const finalConfig = { ...defaultConfig, ...customConfig }

    try {
      await manager.register(plugin, finalConfig)
    } catch (error) {
      console.error(`Failed to register plugin ${plugin.id}:`, error)
    }
  }

  // 初始化插件管理器
  await manager.initialize()

  return manager
}

// 插件类别定义
export const pluginCategories = {
  background: {
    name: '背景处理',
    description: '背景移除、替换、模糊等功能',
    plugins: ['background-removal', 'background-blur', 'background-replacement']
  },
  object: {
    name: '物体编辑',
    description: '物体移除、修复等功能',
    plugins: ['object-removal']
  },
  ai: {
    name: 'AI功能',
    description: '基于AI的智能图像处理功能',
    plugins: ['ai-segmentation', 'ai-replacement']
  },
  enhancement: {
    name: '图像增强',
    description: '图像质量提升、滤镜等功能',
    plugins: []
  },
  custom: {
    name: '自定义',
    description: '用户自定义的插件功能',
    plugins: []
  }
}

// 插件依赖关系
export const pluginDependencies = {
  'background-blur': ['background-removal'],
  'background-replacement': ['background-removal'],
  'ai-replacement': ['ai-segmentation'] // AI替换可能需要先进行分割
}

// 插件兼容性检查
export function checkPluginCompatibility(pluginId: string, availablePlugins: string[]): {
  compatible: boolean
  missingDependencies: string[]
} {
  const dependencies = pluginDependencies[pluginId as keyof typeof pluginDependencies] || []
  const missingDependencies = dependencies.filter(dep => !availablePlugins.includes(dep))
  
  return {
    compatible: missingDependencies.length === 0,
    missingDependencies
  }
}

// 获取推荐的插件组合
export function getRecommendedPluginSets(): Record<string, {
  name: string
  description: string
  plugins: string[]
}> {
  return {
    basic: {
      name: '基础套装',
      description: '包含最常用的图像编辑功能',
      plugins: ['background-removal', 'object-removal', 'background-replacement']
    },
    advanced: {
      name: '高级套装',
      description: '包含所有内置功能',
      plugins: ['background-removal', 'background-blur', 'background-replacement', 'object-removal']
    },
    ai: {
      name: 'AI套装',
      description: '包含所有AI驱动的功能',
      plugins: ['ai-segmentation', 'ai-replacement', 'background-removal']
    },
    complete: {
      name: '完整套装',
      description: '包含所有可用功能',
      plugins: allPlugins.map(p => p.id)
    }
  }
}

// 插件性能统计
export interface PluginStats {
  pluginId: string
  executionCount: number
  totalExecutionTime: number
  averageExecutionTime: number
  successRate: number
  lastUsed: number
}

// 插件使用统计管理器
export class PluginStatsManager {
  private stats: Map<string, PluginStats> = new Map()

  recordExecution(pluginId: string, executionTime: number, success: boolean): void {
    const existing = this.stats.get(pluginId) || {
      pluginId,
      executionCount: 0,
      totalExecutionTime: 0,
      averageExecutionTime: 0,
      successRate: 1,
      lastUsed: 0
    }

    existing.executionCount++
    existing.totalExecutionTime += executionTime
    existing.averageExecutionTime = existing.totalExecutionTime / existing.executionCount
    existing.successRate = (existing.successRate * (existing.executionCount - 1) + (success ? 1 : 0)) / existing.executionCount
    existing.lastUsed = Date.now()

    this.stats.set(pluginId, existing)
  }

  getStats(pluginId: string): PluginStats | null {
    return this.stats.get(pluginId) || null
  }

  getAllStats(): PluginStats[] {
    return Array.from(this.stats.values())
  }

  getMostUsedPlugins(limit = 5): PluginStats[] {
    return this.getAllStats()
      .sort((a, b) => b.executionCount - a.executionCount)
      .slice(0, limit)
  }

  getFastestPlugins(limit = 5): PluginStats[] {
    return this.getAllStats()
      .sort((a, b) => a.averageExecutionTime - b.averageExecutionTime)
      .slice(0, limit)
  }

  getMostReliablePlugins(limit = 5): PluginStats[] {
    return this.getAllStats()
      .sort((a, b) => b.successRate - a.successRate)
      .slice(0, limit)
  }

  clearStats(): void {
    this.stats.clear()
  }

  exportStats(): string {
    return JSON.stringify(Array.from(this.stats.entries()), null, 2)
  }

  importStats(data: string): void {
    try {
      const entries = JSON.parse(data)
      this.stats = new Map(entries)
    } catch (error) {
      console.error('Failed to import plugin stats:', error)
    }
  }
}
