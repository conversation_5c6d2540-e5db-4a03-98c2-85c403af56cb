// 插件管理器核心实现
import { EventBus } from './EventBus'
import type { 
  IPlugin, 
  IPluginManager, 
  PluginConfig, 
  PluginContext, 
  ProcessingResult,
  PluginEvent,
  ImageData
} from './types'

export class PluginManager implements IPluginManager {
  private plugins: Map<string, IPlugin> = new Map()
  private configs: Map<string, PluginConfig> = new Map()
  private results: Map<string, ProcessingResult> = new Map()
  private processingStates: Map<string, boolean> = new Map()
  private cache: Map<string, Map<string, any>> = new Map()
  private eventBus: EventBus = new EventBus()
  
  // 当前图片数据
  private currentImage: ImageData | null = null
  private currentResult: ProcessingResult | null = null

  constructor() {
    this.setupEventHandlers()
  }

  private setupEventHandlers(): void {
    // 监听图片变化事件
    this.eventBus.on('image:changed', (imageData: ImageData | null) => {
      this.currentImage = imageData
      this.notifyPluginsImageChanged(imageData)
    })

    // 监听结果变化事件
    this.eventBus.on('result:changed', (result: ProcessingResult | null) => {
      this.currentResult = result
      this.notifyPluginsResultChanged(result)
    })
  }

  private notifyPluginsImageChanged(imageData: ImageData | null): void {
    this.getEnabledPlugins().forEach(plugin => {
      try {
        plugin.onImageChanged?.(imageData)
      } catch (error) {
        console.error(`Error in plugin ${plugin.id} onImageChanged:`, error)
      }
    })
  }

  private notifyPluginsResultChanged(result: ProcessingResult | null): void {
    this.getEnabledPlugins().forEach(plugin => {
      try {
        plugin.onResultChanged?.(result)
      } catch (error) {
        console.error(`Error in plugin ${plugin.id} onResultChanged:`, error)
      }
    })
  }

  private createPluginContext(pluginId: string): PluginContext {
    return {
      // 图片数据访问
      getCurrentImage: () => this.currentImage,
      getCurrentResult: () => this.currentResult,

      // 状态管理
      setProcessingState: (id: string, isProcessing: boolean) => {
        this.processingStates.set(id, isProcessing)
        this.eventBus.emit('processing:state', { pluginId: id, isProcessing })
      },
      getProcessingState: (id: string) => this.processingStates.get(id) || false,

      // 结果存储
      setResult: (id: string, result: ProcessingResult) => {
        this.results.set(id, result)
        this.currentResult = result
        this.eventBus.emit('result:changed', result)
      },
      getResult: (id: string) => this.results.get(id) || null,
      clearResult: (id: string) => {
        this.results.delete(id)
        if (this.currentResult?.pluginId === id) {
          this.currentResult = null
          this.eventBus.emit('result:changed', null)
        }
      },

      // 缓存管理
      setCache: (id: string, key: string, data: any) => {
        if (!this.cache.has(id)) {
          this.cache.set(id, new Map())
        }
        this.cache.get(id)!.set(key, data)
      },
      getCache: (id: string, key: string) => {
        return this.cache.get(id)?.get(key)
      },
      clearCache: (id: string, key?: string) => {
        if (key) {
          this.cache.get(id)?.delete(key)
        } else {
          this.cache.delete(id)
        }
        this.eventBus.emit('cache:cleared', { pluginId: id, key })
      },

      // 事件系统
      emit: (event: string, data?: any) => this.eventBus.emit(event, data),
      on: (event: string, handler: (data?: any) => void) => this.eventBus.on(event, handler),

      // UI交互
      showToast: (message: string, type = 'info') => {
        this.eventBus.emit('ui:toast', { message, type })
      },
      showModal: (component: React.ComponentType<any>, props?: any) => {
        this.eventBus.emit('ui:modal', { component, props })
      },

      // API调用辅助
      callAPI: async (endpoint: string, options: RequestInit) => {
        return fetch(endpoint, options)
      }
    }
  }

  // 插件注册
  async register(plugin: IPlugin, config?: PluginConfig): Promise<void> {
    if (this.plugins.has(plugin.id)) {
      throw new Error(`Plugin ${plugin.id} is already registered`)
    }

    // 设置默认配置
    const defaultConfig: PluginConfig = {
      enabled: true,
      settings: {},
      order: this.plugins.size,
      ...config
    }

    this.plugins.set(plugin.id, plugin)
    this.configs.set(plugin.id, defaultConfig)

    // 初始化插件
    if (plugin.initialize) {
      const context = this.createPluginContext(plugin.id)
      await plugin.initialize(context)
    }

    this.eventBus.emit('plugin:registered', { plugin, config: defaultConfig })
  }

  async unregister(pluginId: string): Promise<void> {
    const plugin = this.plugins.get(pluginId)
    if (!plugin) {
      return
    }

    // 销毁插件
    if (plugin.destroy) {
      await plugin.destroy()
    }

    // 清理数据
    this.plugins.delete(pluginId)
    this.configs.delete(pluginId)
    this.results.delete(pluginId)
    this.processingStates.delete(pluginId)
    this.cache.delete(pluginId)

    this.eventBus.emit('plugin:unregistered', { pluginId })
  }

  // 插件查询
  getPlugin(pluginId: string): IPlugin | null {
    return this.plugins.get(pluginId) || null
  }

  getAllPlugins(): IPlugin[] {
    return Array.from(this.plugins.values())
  }

  getEnabledPlugins(): IPlugin[] {
    return Array.from(this.plugins.entries())
      .filter(([id]) => this.configs.get(id)?.enabled)
      .sort(([idA], [idB]) => {
        const orderA = this.configs.get(idA)?.order || 0
        const orderB = this.configs.get(idB)?.order || 0
        return orderA - orderB
      })
      .map(([, plugin]) => plugin)
  }

  // 插件配置
  setPluginConfig(pluginId: string, config: Partial<PluginConfig>): void {
    const currentConfig = this.configs.get(pluginId)
    if (currentConfig) {
      this.configs.set(pluginId, { ...currentConfig, ...config })
    }
  }

  getPluginConfig(pluginId: string): PluginConfig | null {
    return this.configs.get(pluginId) || null
  }

  // 动作执行
  async executeAction(pluginId: string, actionId: string, options?: any): Promise<ProcessingResult> {
    const plugin = this.getPlugin(pluginId)
    if (!plugin) {
      throw new Error(`Plugin ${pluginId} not found`)
    }

    const action = plugin.getActions().find(a => a.id === actionId)
    if (!action) {
      throw new Error(`Action ${actionId} not found in plugin ${pluginId}`)
    }

    const context = this.createPluginContext(pluginId)
    
    // 检查动作可用性
    if (action.isAvailable && !action.isAvailable(context)) {
      throw new Error(`Action ${actionId} is not available`)
    }

    this.eventBus.emit('processing:start', { pluginId, actionId })
    
    try {
      const result = await action.execute(context, options)
      this.eventBus.emit('processing:end', { pluginId, actionId, result })
      return result
    } catch (error) {
      this.eventBus.emit('processing:error', { pluginId, actionId, error })
      throw error
    }
  }

  // 事件系统
  emit(event: PluginEvent, data?: any): void {
    this.eventBus.emit(event, data)
  }

  on(event: PluginEvent, handler: (data?: any) => void): () => void {
    return this.eventBus.on(event, handler)
  }

  // 生命周期
  async initialize(): Promise<void> {
    // 初始化所有已注册的插件
    const initPromises = Array.from(this.plugins.entries()).map(async ([id, plugin]) => {
      if (plugin.initialize) {
        const context = this.createPluginContext(id)
        await plugin.initialize(context)
      }
    })

    await Promise.all(initPromises)
  }

  async destroy(): Promise<void> {
    // 销毁所有插件
    const destroyPromises = Array.from(this.plugins.values()).map(plugin => 
      plugin.destroy?.()
    )

    await Promise.all(destroyPromises)

    // 清理所有数据
    this.plugins.clear()
    this.configs.clear()
    this.results.clear()
    this.processingStates.clear()
    this.cache.clear()
    this.eventBus.destroy()
  }

  // 设置当前图片
  setCurrentImage(imageData: ImageData | null): void {
    this.currentImage = imageData
    this.eventBus.emit('image:changed', imageData)
  }

  // 获取所有结果
  getAllResults(): ProcessingResult[] {
    return Array.from(this.results.values())
  }

  // 获取最新结果
  getLatestResult(): ProcessingResult | null {
    const results = this.getAllResults()
    if (results.length === 0) return null
    
    return results.reduce((latest, current) => 
      current.timestamp > latest.timestamp ? current : latest
    )
  }
}
