// 事件总线系统
export type EventHandler<T = any> = (data: T) => void

export class EventBus {
  private listeners: Map<string, Set<EventHandler>> = new Map()
  private onceListeners: Map<string, Set<EventHandler>> = new Map()

  /**
   * 注册事件监听器
   */
  on<T = any>(event: string, handler: EventHandler<T>): () => void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set())
    }
    
    this.listeners.get(event)!.add(handler)
    
    // 返回取消监听的函数
    return () => {
      this.off(event, handler)
    }
  }

  /**
   * 注册一次性事件监听器
   */
  once<T = any>(event: string, handler: EventHandler<T>): () => void {
    if (!this.onceListeners.has(event)) {
      this.onceListeners.set(event, new Set())
    }
    
    this.onceListeners.get(event)!.add(handler)
    
    // 返回取消监听的函数
    return () => {
      this.onceListeners.get(event)?.delete(handler)
    }
  }

  /**
   * 移除事件监听器
   */
  off<T = any>(event: string, handler: EventHandler<T>): void {
    this.listeners.get(event)?.delete(handler)
    this.onceListeners.get(event)?.delete(handler)
  }

  /**
   * 移除所有事件监听器
   */
  removeAllListeners(event?: string): void {
    if (event) {
      this.listeners.delete(event)
      this.onceListeners.delete(event)
    } else {
      this.listeners.clear()
      this.onceListeners.clear()
    }
  }

  /**
   * 触发事件
   */
  emit<T = any>(event: string, data?: T): void {
    // 触发普通监听器
    const handlers = this.listeners.get(event)
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(data)
        } catch (error) {
          console.error(`Error in event handler for "${event}":`, error)
        }
      })
    }

    // 触发一次性监听器
    const onceHandlers = this.onceListeners.get(event)
    if (onceHandlers) {
      onceHandlers.forEach(handler => {
        try {
          handler(data)
        } catch (error) {
          console.error(`Error in once event handler for "${event}":`, error)
        }
      })
      // 清除一次性监听器
      this.onceListeners.delete(event)
    }
  }

  /**
   * 异步触发事件
   */
  async emitAsync<T = any>(event: string, data?: T): Promise<void> {
    const promises: Promise<void>[] = []

    // 处理普通监听器
    const handlers = this.listeners.get(event)
    if (handlers) {
      handlers.forEach(handler => {
        promises.push(
          Promise.resolve().then(() => handler(data))
        )
      })
    }

    // 处理一次性监听器
    const onceHandlers = this.onceListeners.get(event)
    if (onceHandlers) {
      onceHandlers.forEach(handler => {
        promises.push(
          Promise.resolve().then(() => handler(data))
        )
      })
      // 清除一次性监听器
      this.onceListeners.delete(event)
    }

    await Promise.all(promises)
  }

  /**
   * 检查是否有监听器
   */
  hasListeners(event: string): boolean {
    const hasNormal = this.listeners.has(event) && this.listeners.get(event)!.size > 0
    const hasOnce = this.onceListeners.has(event) && this.onceListeners.get(event)!.size > 0
    return hasNormal || hasOnce
  }

  /**
   * 获取事件监听器数量
   */
  getListenerCount(event: string): number {
    const normalCount = this.listeners.get(event)?.size || 0
    const onceCount = this.onceListeners.get(event)?.size || 0
    return normalCount + onceCount
  }

  /**
   * 获取所有事件名称
   */
  getEventNames(): string[] {
    const events = new Set<string>()
    this.listeners.forEach((_, event) => events.add(event))
    this.onceListeners.forEach((_, event) => events.add(event))
    return Array.from(events)
  }

  /**
   * 销毁事件总线
   */
  destroy(): void {
    this.removeAllListeners()
  }
}

// 创建全局事件总线实例
export const globalEventBus = new EventBus()
