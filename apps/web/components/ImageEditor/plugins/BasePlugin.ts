// 插件基类，提供通用功能
import type {
  IPlugin,
  PluginContext,
  PluginAction,
  ToolbarItem,
  SettingsPanelProps,
  ProcessingResult,
} from './types'
import type { ImageData } from '../index'

export abstract class BasePlugin implements IPlugin {
  // 插件元信息（子类必须实现）
  abstract readonly id: string
  abstract readonly name: string
  abstract readonly version: string
  abstract readonly description: string

  // 可选元信息
  readonly author?: string
  readonly icon?: React.ComponentType<any>

  // 插件上下文
  protected context?: PluginContext

  // 插件生命周期
  async initialize(context: PluginContext): Promise<void> {
    this.context = context
    await this.onInitialize(context)
  }

  async destroy(): Promise<void> {
    await this.onDestroy()
    this.context = undefined
  }

  // 子类可重写的生命周期方法
  protected async onInitialize(context: PluginContext): Promise<void> {
    // 默认空实现
  }

  protected async onDestroy(): Promise<void> {
    // 默认空实现
  }

  // 插件功能定义（子类必须实现）
  abstract getActions(): PluginAction[]

  // 可选功能
  getToolbarItems(): ToolbarItem[] {
    return []
  }

  getSettingsPanel(): React.ComponentType<SettingsPanelProps> | null {
    return null
  }

  // 事件处理（子类可重写）
  onImageChanged(imageData: ImageData | null): void {
    // 默认空实现
  }

  onResultChanged(result: ProcessingResult | null): void {
    // 默认空实现
  }

  // 辅助方法
  protected getCurrentImage(): ImageData | null {
    return this.context?.getCurrentImage() || null
  }

  protected getCurrentResult(): ProcessingResult | null {
    return this.context?.getCurrentResult() || null
  }

  protected setProcessingState(isProcessing: boolean): void {
    this.context?.setProcessingState(this.id, isProcessing)
  }

  protected getProcessingState(): boolean {
    return this.context?.getProcessingState(this.id) || false
  }

  protected setResult(result: ProcessingResult): void {
    this.context?.setResult(this.id, result)
  }

  protected getResult(): ProcessingResult | null {
    return this.context?.getResult(this.id) || null
  }

  protected clearResult(): void {
    this.context?.clearResult(this.id)
  }

  protected setCache(key: string, data: any): void {
    this.context?.setCache(this.id, key, data)
  }

  protected getCache(key: string): any {
    return this.context?.getCache(this.id, key)
  }

  protected clearCache(key?: string): void {
    this.context?.clearCache(this.id, key)
  }

  protected emit(event: string, data?: any): void {
    this.context?.emit(event, data)
  }

  protected on(
    event: string,
    handler: (data?: any) => void
  ): (() => void) | undefined {
    return this.context?.on(event, handler)
  }

  protected showToast(
    message: string,
    type?: 'success' | 'error' | 'warning' | 'info'
  ): void {
    this.context?.showToast(message, type)
  }

  protected showModal(component: React.ComponentType<any>, props?: any): void {
    this.context?.showModal(component, props)
  }

  protected async callAPI(
    endpoint: string,
    options: RequestInit
  ): Promise<Response> {
    if (!this.context) {
      throw new Error('Plugin context not available')
    }
    return this.context.callAPI(endpoint, options)
  }

  // 创建处理结果的辅助方法
  protected createResult(
    actionId: string,
    imageUrl: string,
    metadata?: Record<string, any>,
    cacheData?: Record<string, any>
  ): ProcessingResult {
    return {
      id: `${this.id}_${actionId}_${Date.now()}`,
      pluginId: this.id,
      actionId,
      imageUrl,
      timestamp: Date.now(),
      type: 'image',
      metadata,
      cacheData,
    }
  }

  // 图片处理的通用辅助方法
  protected async loadImage(url: string): Promise<HTMLImageElement> {
    return new Promise((resolve, reject) => {
      const img = new Image()
      img.crossOrigin = 'anonymous'
      img.onload = () => resolve(img)
      img.onerror = reject
      img.src = url
    })
  }

  protected createCanvas(width: number, height: number): HTMLCanvasElement {
    const canvas = document.createElement('canvas')
    canvas.width = width
    canvas.height = height
    return canvas
  }

  protected canvasToDataURL(
    canvas: HTMLCanvasElement,
    type = 'image/png',
    quality = 1
  ): string {
    return canvas.toDataURL(type, quality)
  }

  protected canvasToBase64(
    canvas: HTMLCanvasElement,
    type = 'image/png',
    quality = 1
  ): string {
    return this.canvasToDataURL(canvas, type, quality).split(',')[1]
  }

  protected async blobToBase64(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => {
        const result = reader.result as string
        resolve(result.split(',')[1])
      }
      reader.onerror = reject
      reader.readAsDataURL(blob)
    })
  }

  protected base64ToBlob(base64: string, type = 'image/png'): Blob {
    const byteCharacters = atob(base64)
    const byteNumbers = new Array(byteCharacters.length)
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i)
    }
    const byteArray = new Uint8Array(byteNumbers)
    return new Blob([byteArray], { type })
  }

  // 验证当前图片是否可用
  protected validateCurrentImage(): ImageData {
    const image = this.getCurrentImage()
    if (!image) {
      throw new Error('No image selected')
    }
    return image
  }

  // 生成唯一ID
  protected generateId(): string {
    return `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }
}
