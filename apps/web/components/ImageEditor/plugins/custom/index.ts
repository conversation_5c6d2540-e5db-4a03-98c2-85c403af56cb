// 自定义插件注册中心
import { AISegmentationPlugin } from './AISegmentationPlugin'
import { AIReplacementPlugin } from './AIReplacementPlugin'
import type { IPlugin } from '../types'

// 自定义插件列表
export const customPlugins: IPlugin[] = [
  new AISegmentationPlugin(),
  new AIReplacementPlugin()
]

// 自定义插件默认配置
export const customPluginConfigs = {
  'ai-segmentation': {
    enabled: true,
    settings: {
      defaultModel: 'sam',
      autoPostProcess: true,
      cacheResults: true
    },
    order: 10
  },
  'ai-replacement': {
    enabled: true,
    settings: {
      defaultStrength: 0.8,
      defaultGuidanceScale: 7.5,
      enableQuickPrompt: true
    },
    order: 11
  }
}

// 导出所有自定义插件类
export {
  AISegmentationPlugin,
  AIReplacementPlugin
}
