# 图片编辑器插件系统

这是一个可扩展的图片编辑插件系统，支持动态加载和管理各种图片处理功能。

## 系统特性

- 🔌 **插件化架构** - 易于扩展和维护
- 🚀 **智能缓存** - 自动缓存处理结果，提高性能
- 📡 **事件驱动** - 插件间通过事件系统通信
- 🎨 **统一UI** - 自动生成插件界面
- ⚙️ **灵活配置** - 支持插件配置和管理
- 🔄 **依赖管理** - 自动处理插件依赖关系

## 快速开始

### 基本使用

```typescript
import { createPluginManager } from './plugins'

// 创建插件管理器（包含所有插件）
const pluginManager = await createPluginManager()

// 设置当前图片
pluginManager.setCurrentImage(imageData)

// 执行插件动作
const result = await pluginManager.executeAction(
  'background-removal',
  'remove-background',
  { model: 'u2net' }
)
```

### 选择性加载插件

```typescript
// 只加载指定插件
const pluginManager = await createPluginManager([
  'background-removal',
  'background-blur',
  'ai-segmentation'
])
```

### 自定义插件配置

```typescript
const pluginManager = await createPluginManager(undefined, {
  'background-removal': {
    enabled: true,
    settings: { defaultModel: 'u2netp' },
    order: 0
  }
})
```

## 内置插件

### 背景处理插件

1. **背景移除插件** (`background-removal`)
   - 智能移除图片背景
   - 支持多种AI模型
   - 自动缓存结果

2. **背景模糊插件** (`background-blur`)
   - 对背景应用模糊效果
   - 实时调整模糊强度
   - 依赖背景移除插件

3. **背景替换插件** (`background-replacement`)
   - 替换图片背景
   - 支持预设和自定义背景
   - 透明背景支持

### 物体编辑插件

4. **物体移除插件** (`object-removal`)
   - 智能移除图片中的物体
   - 基于mask的精确控制
   - AI填充背景

## 自定义插件

### AI功能插件

5. **AI智能抠图插件** (`ai-segmentation`)
   - 自动分割图片中的物体
   - 支持多种AI模型
   - 智能抠图功能

6. **AI智能替换插件** (`ai-replacement`)
   - 基于文本描述替换内容
   - 风格迁移功能
   - 场景替换

## 开发自定义插件

### 1. 继承BasePlugin类

```typescript
import { BasePlugin } from './BasePlugin'
import type { PluginAction, ProcessingResult } from './types'

export class MyCustomPlugin extends BasePlugin {
  readonly id = 'my-custom-plugin'
  readonly name = '我的自定义插件'
  readonly version = '1.0.0'
  readonly description = '这是一个自定义插件示例'

  getActions(): PluginAction[] {
    return [
      {
        id: 'my-action',
        name: '我的动作',
        description: '执行自定义处理',
        category: 'custom',
        execute: this.executeMyAction.bind(this),
        isAvailable: () => this.getCurrentImage() !== null
      }
    ]
  }

  private async executeMyAction(): Promise<ProcessingResult> {
    const currentImage = this.validateCurrentImage()
    
    // 执行自定义处理逻辑
    const processedImageUrl = await this.processImage(currentImage)
    
    return this.createResult(
      'my-action',
      processedImageUrl,
      { customMetadata: 'value' }
    )
  }

  private async processImage(image: any): Promise<string> {
    // 实现你的图片处理逻辑
    return 'data:image/png;base64,processed-image-data'
  }
}
```

### 2. 注册插件

```typescript
import { PluginManager } from './PluginManager'
import { MyCustomPlugin } from './MyCustomPlugin'

const pluginManager = new PluginManager()
const myPlugin = new MyCustomPlugin()

await pluginManager.register(myPlugin, {
  enabled: true,
  settings: {},
  order: 10
})
```

### 3. 添加工具栏组件（可选）

```typescript
getToolbarItems(): ToolbarItem[] {
  return [
    {
      id: 'my-toolbar-item',
      name: '我的工具',
      component: MyToolbarComponent,
      position: 'right',
      order: 10
    }
  ]
}
```

## 插件API参考

### PluginContext

插件上下文提供以下API：

```typescript
interface PluginContext {
  // 图片数据访问
  getCurrentImage(): ImageData | null
  getCurrentResult(): ProcessingResult | null
  
  // 状态管理
  setProcessingState(pluginId: string, isProcessing: boolean): void
  getProcessingState(pluginId: string): boolean
  
  // 结果存储
  setResult(pluginId: string, result: ProcessingResult): void
  getResult(pluginId: string): ProcessingResult | null
  clearResult(pluginId: string): void
  
  // 缓存管理
  setCache(pluginId: string, key: string, data: any): void
  getCache(pluginId: string, key: string): any
  clearCache(pluginId: string, key?: string): void
  
  // 事件系统
  emit(event: string, data?: any): void
  on(event: string, handler: (data?: any) => void): () => void
  
  // UI交互
  showToast(message: string, type?: 'success' | 'error' | 'warning' | 'info'): void
  showModal(component: React.ComponentType<any>, props?: any): void
  
  // API调用辅助
  callAPI(endpoint: string, options: RequestInit): Promise<Response>
}
```

### BasePlugin辅助方法

```typescript
// 图片处理辅助
protected async loadImage(url: string): Promise<HTMLImageElement>
protected createCanvas(width: number, height: number): HTMLCanvasElement
protected canvasToDataURL(canvas: HTMLCanvasElement): string
protected canvasToBase64(canvas: HTMLCanvasElement): string

// 状态管理辅助
protected getCurrentImage(): ImageData | null
protected getCurrentResult(): ProcessingResult | null
protected setProcessingState(isProcessing: boolean): void
protected setResult(result: ProcessingResult): void
protected setCache(key: string, data: any): void
protected getCache(key: string): any

// UI交互辅助
protected showToast(message: string, type?: string): void
protected showModal(component: React.ComponentType<any>, props?: any): void

// 结果创建辅助
protected createResult(
  actionId: string,
  imageUrl: string,
  metadata?: Record<string, any>,
  cacheData?: Record<string, any>
): ProcessingResult
```

## 事件系统

插件可以通过事件系统进行通信：

```typescript
// 发送事件
this.emit('custom-event', { data: 'value' })

// 监听事件
const unsubscribe = this.on('image:changed', (imageData) => {
  console.log('Image changed:', imageData)
})

// 取消监听
unsubscribe()
```

### 内置事件

- `image:changed` - 图片变化
- `result:changed` - 处理结果变化
- `processing:start` - 开始处理
- `processing:end` - 处理完成
- `processing:progress` - 处理进度
- `plugin:registered` - 插件注册
- `plugin:unregistered` - 插件注销
- `cache:cleared` - 缓存清除

## 最佳实践

### 1. 错误处理

```typescript
private async executeMyAction(): Promise<ProcessingResult> {
  this.setProcessingState(true)
  
  try {
    // 处理逻辑
    const result = await this.processImage()
    this.showToast('处理完成', 'success')
    return result
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '处理失败'
    this.showToast(errorMessage, 'error')
    throw error
  } finally {
    this.setProcessingState(false)
  }
}
```

### 2. 缓存使用

```typescript
private async executeWithCache(): Promise<ProcessingResult> {
  const cacheKey = `processed_${this.getCurrentImage()?.id}`
  const cached = this.getCache(cacheKey)
  
  if (cached) {
    this.showToast('使用缓存结果', 'info')
    return cached
  }
  
  const result = await this.processImage()
  this.setCache(cacheKey, result)
  
  return result
}
```

### 3. 依赖管理

```typescript
protected async onInitialize(): Promise<void> {
  // 获取依赖插件的引用
  this.dependencyPlugin = this.context?.getPlugin?.('dependency-plugin-id')
  
  if (!this.dependencyPlugin) {
    console.warn('Dependency plugin not found')
  }
}
```

## 性能优化

1. **智能缓存** - 自动缓存处理结果，避免重复计算
2. **懒加载** - 插件按需加载和初始化
3. **事件防抖** - 避免频繁的事件触发
4. **内存管理** - 及时清理不需要的缓存和资源

## 故障排除

### 常见问题

1. **插件注册失败**
   - 检查插件ID是否唯一
   - 确认插件类正确继承BasePlugin

2. **动作执行失败**
   - 检查动作的isAvailable方法
   - 确认必要的依赖插件已加载

3. **缓存问题**
   - 使用clearCache方法清除过期缓存
   - 检查缓存键的命名规范

### 调试技巧

```typescript
// 启用调试模式
const pluginManager = new PluginManager()
pluginManager.on('*', (event, data) => {
  console.log('Plugin Event:', event, data)
})
```

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 编写测试
4. 提交代码
5. 创建Pull Request

## 许可证

MIT License
