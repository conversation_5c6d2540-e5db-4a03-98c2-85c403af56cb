// 插件系统测试
import { PluginManager } from '../PluginManager'
import { BackgroundRemovalPlugin } from '../builtin/BackgroundRemovalPlugin'
import { BackgroundBlurPlugin } from '../builtin/BackgroundBlurPlugin'
import { ObjectRemovalPlugin } from '../builtin/ObjectRemovalPlugin'
import { BackgroundReplacementPlugin } from '../builtin/BackgroundReplacementPlugin'
import { AISegmentationPlugin } from '../custom/AISegmentationPlugin'
import { AIReplacementPlugin } from '../custom/AIReplacementPlugin'
import { createPluginManager, checkPluginCompatibility } from '../index'
import type { ImageData } from '../../index'

// Mock image data for testing
const mockImageData: ImageData = {
  id: 'test-image-1',
  file: new File([''], 'test.jpg', { type: 'image/jpeg' }),
  url: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=',
  width: 800,
  height: 600,
  name: 'test.jpg'
}

describe('Plugin System', () => {
  let pluginManager: PluginManager

  beforeEach(() => {
    pluginManager = new PluginManager()
  })

  afterEach(async () => {
    await pluginManager.destroy()
  })

  describe('Plugin Registration', () => {
    test('should register builtin plugins successfully', async () => {
      const backgroundRemovalPlugin = new BackgroundRemovalPlugin()
      
      await pluginManager.register(backgroundRemovalPlugin, {
        enabled: true,
        settings: {},
        order: 0
      })

      const registeredPlugin = pluginManager.getPlugin('background-removal')
      expect(registeredPlugin).toBe(backgroundRemovalPlugin)
      expect(registeredPlugin?.name).toBe('背景移除')
    })

    test('should register custom plugins successfully', async () => {
      const aiSegmentationPlugin = new AISegmentationPlugin()
      
      await pluginManager.register(aiSegmentationPlugin, {
        enabled: true,
        settings: {},
        order: 0
      })

      const registeredPlugin = pluginManager.getPlugin('ai-segmentation')
      expect(registeredPlugin).toBe(aiSegmentationPlugin)
      expect(registeredPlugin?.name).toBe('AI智能抠图')
    })

    test('should prevent duplicate plugin registration', async () => {
      const plugin1 = new BackgroundRemovalPlugin()
      const plugin2 = new BackgroundRemovalPlugin()

      await pluginManager.register(plugin1)
      
      await expect(pluginManager.register(plugin2)).rejects.toThrow(
        'Plugin background-removal is already registered'
      )
    })
  })

  describe('Plugin Actions', () => {
    test('should execute background removal action', async () => {
      const plugin = new BackgroundRemovalPlugin()
      await pluginManager.register(plugin)
      await pluginManager.initialize()

      // Set current image
      pluginManager.setCurrentImage(mockImageData)

      // Mock the API call
      jest.spyOn(plugin as any, 'executeRemoveBackground').mockResolvedValue({
        id: 'result-1',
        pluginId: 'background-removal',
        actionId: 'remove-background',
        imageUrl: 'data:image/png;base64,mock-result',
        timestamp: Date.now(),
        type: 'image'
      })

      const result = await pluginManager.executeAction(
        'background-removal',
        'remove-background',
        { model: 'u2net' }
      )

      expect(result.pluginId).toBe('background-removal')
      expect(result.actionId).toBe('remove-background')
      expect(result.imageUrl).toContain('data:image/png;base64,')
    })

    test('should handle plugin action errors gracefully', async () => {
      const plugin = new BackgroundRemovalPlugin()
      await pluginManager.register(plugin)
      await pluginManager.initialize()

      // Mock API failure
      jest.spyOn(plugin as any, 'executeRemoveBackground').mockRejectedValue(
        new Error('API call failed')
      )

      pluginManager.setCurrentImage(mockImageData)

      await expect(
        pluginManager.executeAction('background-removal', 'remove-background')
      ).rejects.toThrow('API call failed')
    })
  })

  describe('Plugin Dependencies', () => {
    test('should check plugin compatibility correctly', () => {
      const availablePlugins = ['background-removal', 'object-removal']
      
      // Background blur depends on background removal
      const blurCompatibility = checkPluginCompatibility('background-blur', availablePlugins)
      expect(blurCompatibility.compatible).toBe(true)
      expect(blurCompatibility.missingDependencies).toEqual([])

      // Test with missing dependency
      const availablePluginsWithoutBgRemoval = ['object-removal']
      const blurCompatibilityMissing = checkPluginCompatibility('background-blur', availablePluginsWithoutBgRemoval)
      expect(blurCompatibilityMissing.compatible).toBe(false)
      expect(blurCompatibilityMissing.missingDependencies).toContain('background-removal')
    })
  })

  describe('Event System', () => {
    test('should emit and handle events correctly', async () => {
      const plugin = new BackgroundRemovalPlugin()
      await pluginManager.register(plugin)

      let eventReceived = false
      let eventData: any = null

      const unsubscribe = pluginManager.on('test-event', (data) => {
        eventReceived = true
        eventData = data
      })

      pluginManager.emit('test-event', { message: 'test' })

      expect(eventReceived).toBe(true)
      expect(eventData).toEqual({ message: 'test' })

      unsubscribe()
    })

    test('should handle image change events', async () => {
      const plugin = new BackgroundRemovalPlugin()
      let imageChangeReceived = false

      // Mock the onImageChanged method
      jest.spyOn(plugin, 'onImageChanged').mockImplementation((imageData) => {
        imageChangeReceived = true
      })

      await pluginManager.register(plugin)
      await pluginManager.initialize()

      pluginManager.setCurrentImage(mockImageData)

      expect(imageChangeReceived).toBe(true)
    })
  })

  describe('Plugin Manager Factory', () => {
    test('should create plugin manager with all plugins', async () => {
      const manager = await createPluginManager()
      
      const allPlugins = manager.getAllPlugins()
      expect(allPlugins.length).toBeGreaterThan(0)
      
      // Check that builtin plugins are registered
      expect(manager.getPlugin('background-removal')).toBeTruthy()
      expect(manager.getPlugin('background-blur')).toBeTruthy()
      expect(manager.getPlugin('object-removal')).toBeTruthy()
      expect(manager.getPlugin('background-replacement')).toBeTruthy()
      
      // Check that custom plugins are registered
      expect(manager.getPlugin('ai-segmentation')).toBeTruthy()
      expect(manager.getPlugin('ai-replacement')).toBeTruthy()

      await manager.destroy()
    })

    test('should create plugin manager with selected plugins only', async () => {
      const enabledPlugins = ['background-removal', 'ai-segmentation']
      const manager = await createPluginManager(enabledPlugins)
      
      const allPlugins = manager.getAllPlugins()
      expect(allPlugins.length).toBe(2)
      
      expect(manager.getPlugin('background-removal')).toBeTruthy()
      expect(manager.getPlugin('ai-segmentation')).toBeTruthy()
      expect(manager.getPlugin('background-blur')).toBeFalsy()

      await manager.destroy()
    })
  })

  describe('Plugin Configuration', () => {
    test('should apply custom plugin configurations', async () => {
      const plugin = new BackgroundRemovalPlugin()
      const customConfig = {
        enabled: false,
        settings: { defaultModel: 'u2netp' },
        order: 5
      }

      await pluginManager.register(plugin, customConfig)

      const config = pluginManager.getPluginConfig('background-removal')
      expect(config?.enabled).toBe(false)
      expect(config?.settings.defaultModel).toBe('u2netp')
      expect(config?.order).toBe(5)
    })

    test('should update plugin configuration', async () => {
      const plugin = new BackgroundRemovalPlugin()
      await pluginManager.register(plugin)

      pluginManager.setPluginConfig('background-removal', {
        enabled: false,
        order: 10
      })

      const config = pluginManager.getPluginConfig('background-removal')
      expect(config?.enabled).toBe(false)
      expect(config?.order).toBe(10)
    })
  })

  describe('Plugin Results Management', () => {
    test('should store and retrieve plugin results', async () => {
      const plugin = new BackgroundRemovalPlugin()
      await pluginManager.register(plugin)
      await pluginManager.initialize()

      pluginManager.setCurrentImage(mockImageData)

      const mockResult = {
        id: 'result-1',
        pluginId: 'background-removal',
        actionId: 'remove-background',
        imageUrl: 'data:image/png;base64,mock-result',
        timestamp: Date.now(),
        type: 'image' as const
      }

      // Simulate storing a result
      const context = (pluginManager as any).createPluginContext('background-removal')
      context.setResult('background-removal', mockResult)

      const latestResult = pluginManager.getLatestResult()
      expect(latestResult).toEqual(mockResult)

      const allResults = pluginManager.getAllResults()
      expect(allResults).toContain(mockResult)
    })
  })
})

// Integration tests
describe('Plugin System Integration', () => {
  test('should handle complete workflow', async () => {
    const manager = await createPluginManager(['background-removal', 'background-blur'])
    
    // Set image
    manager.setCurrentImage(mockImageData)

    // Mock API calls
    const bgRemovalPlugin = manager.getPlugin('background-removal') as BackgroundRemovalPlugin
    const bgBlurPlugin = manager.getPlugin('background-blur') as BackgroundBlurPlugin

    jest.spyOn(bgRemovalPlugin as any, 'executeRemoveBackground').mockResolvedValue({
      id: 'bg-removal-result',
      pluginId: 'background-removal',
      actionId: 'remove-background',
      imageUrl: 'data:image/png;base64,bg-removed',
      timestamp: Date.now(),
      type: 'image'
    })

    jest.spyOn(bgBlurPlugin as any, 'executeBlurBackground').mockResolvedValue({
      id: 'bg-blur-result',
      pluginId: 'background-blur',
      actionId: 'blur-background',
      imageUrl: 'data:image/png;base64,bg-blurred',
      timestamp: Date.now() + 1000,
      type: 'image'
    })

    // Execute background removal
    const bgRemovalResult = await manager.executeAction('background-removal', 'remove-background')
    expect(bgRemovalResult.pluginId).toBe('background-removal')

    // Execute background blur (should use the background removal result)
    const bgBlurResult = await manager.executeAction('background-blur', 'blur-background', {
      blurIntensity: 30
    })
    expect(bgBlurResult.pluginId).toBe('background-blur')

    // Latest result should be the blur result (newer timestamp)
    const latestResult = manager.getLatestResult()
    expect(latestResult?.pluginId).toBe('background-blur')

    await manager.destroy()
  })
})
