// 内置插件注册中心
import { BackgroundRemovalPlugin } from './BackgroundRemovalPlugin'
import { BackgroundBlurPlugin } from './BackgroundBlurPlugin'
import { ObjectRemovalPlugin } from './ObjectRemovalPlugin'
import { BackgroundReplacementPlugin } from './BackgroundReplacementPlugin'
import type { IPlugin } from '../types'

// 内置插件列表
export const builtinPlugins: IPlugin[] = [
  new BackgroundRemovalPlugin(),
  new BackgroundBlurPlugin(),
  new ObjectRemovalPlugin(),
  new BackgroundReplacementPlugin()
]

// 按类别分组的插件
export const pluginsByCategory = {
  background: [
    'background-removal',
    'background-blur', 
    'background-replacement'
  ],
  object: [
    'object-removal'
  ],
  enhancement: [],
  ai: [],
  custom: []
}

// 插件依赖关系
export const pluginDependencies = {
  'background-blur': ['background-removal'],
  'background-replacement': ['background-removal']
}

// 插件默认配置
export const defaultPluginConfigs = {
  'background-removal': {
    enabled: true,
    settings: {
      defaultModel: 'u2net',
      cacheResults: true
    },
    order: 0
  },
  'background-blur': {
    enabled: true,
    settings: {
      defaultIntensity: 20,
      realTimePreview: true
    },
    order: 1
  },
  'object-removal': {
    enabled: true,
    settings: {
      defaultPrompt: 'high quality, photorealistic, seamless background',
      defaultNegativePrompt: 'blurry, low quality, distorted, artifacts'
    },
    order: 2
  },
  'background-replacement': {
    enabled: true,
    settings: {
      showDefaultBackgrounds: true,
      allowCustomUpload: true
    },
    order: 3
  }
}

// 导出所有插件类，方便单独使用
export {
  BackgroundRemovalPlugin,
  BackgroundBlurPlugin,
  ObjectRemovalPlugin,
  BackgroundReplacementPlugin
}
