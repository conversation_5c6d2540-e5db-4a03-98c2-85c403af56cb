// 背景替换插件
import React from 'react'
import { Image, Palette } from 'lucide-react'
import { BasePlugin } from '../BasePlugin'
import { compositeWithBackgroundSecure } from '../../lib/api-client'
import { BackgroundRemovalPlugin } from './BackgroundRemovalPlugin'
import { getDefaultBackgrounds } from '../../lib/background-removal'
import type { PluginAction, ActionOption, ProcessingResult, ToolbarItem, ToolbarItemProps } from '../types'
import { BackgroundSelector } from '../../components/BackgroundSelector'

export class BackgroundReplacementPlugin extends BasePlugin {
  readonly id = 'background-replacement'
  readonly name = '背景替换'
  readonly version = '1.0.0'
  readonly description = '替换图片背景为预设或自定义背景'
  readonly author = 'Faith Magic'
  readonly icon = Image

  private backgroundRemovalPlugin?: BackgroundRemovalPlugin

  protected async onInitialize(): Promise<void> {
    // 获取背景移除插件的引用
    this.backgroundRemovalPlugin = this.context?.getPlugin?.('background-removal') as BackgroundRemovalPlugin
  }

  getActions(): PluginAction[] {
    return [
      {
        id: 'replace-background',
        name: '替换背景',
        description: '将图片背景替换为指定的背景图片',
        icon: Image,
        category: 'background',
        execute: this.executeReplaceBackground.bind(this),
        isAvailable: this.isReplaceBackgroundAvailable.bind(this),
        getOptions: this.getReplaceBackgroundOptions.bind(this)
      },
      {
        id: 'transparent-background',
        name: '透明背景',
        description: '移除背景，保持透明',
        icon: Palette,
        category: 'background',
        execute: this.executeTransparentBackground.bind(this),
        isAvailable: this.isTransparentBackgroundAvailable.bind(this)
      }
    ]
  }

  getToolbarItems(): ToolbarItem[] {
    return [
      {
        id: 'background-selector',
        name: '背景选择器',
        icon: Image,
        component: BackgroundSelectorWrapper,
        position: 'right',
        order: 20
      }
    ]
  }

  private isReplaceBackgroundAvailable(): boolean {
    return this.getCurrentImage() !== null
  }

  private isTransparentBackgroundAvailable(): boolean {
    return this.getCurrentImage() !== null
  }

  private getReplaceBackgroundOptions(): ActionOption[] {
    const defaultBackgrounds = getDefaultBackgrounds()
    
    return [
      {
        key: 'backgroundUrl',
        name: '背景图片',
        type: 'select',
        defaultValue: defaultBackgrounds[0].url,
        options: defaultBackgrounds.map(bg => ({
          label: bg.name,
          value: bg.url
        })),
        description: '选择要替换的背景图片'
      },
      {
        key: 'customBackground',
        name: '自定义背景',
        type: 'string',
        defaultValue: '',
        description: '输入自定义背景图片URL（可选）'
      }
    ]
  }

  private async executeReplaceBackground(context: any, options: any = {}): Promise<ProcessingResult> {
    const currentImage = this.validateCurrentImage()
    const { backgroundUrl, customBackground } = options
    
    const finalBackgroundUrl = customBackground || backgroundUrl
    if (!finalBackgroundUrl) {
      throw new Error('Please specify a background image')
    }

    this.setProcessingState(true)

    try {
      // 获取前景图片
      const foregroundImageUrl = await this.ensureForegroundAvailable()

      // 检查是否是透明背景（直接返回前景图片）
      if (this.isTransparentBackground(finalBackgroundUrl)) {
        const result = this.createResult(
          'replace-background',
          foregroundImageUrl,
          { backgroundUrl: finalBackgroundUrl, transparent: true }
        )
        
        this.showToast('背景已设为透明', 'success')
        return result
      }

      // 合成前景和背景
      const resultImageUrl = await compositeWithBackgroundSecure(
        foregroundImageUrl,
        finalBackgroundUrl,
        currentImage.width,
        currentImage.height
      )

      const result = this.createResult(
        'replace-background',
        resultImageUrl,
        { 
          backgroundUrl: finalBackgroundUrl,
          foregroundUrl: foregroundImageUrl,
          transparent: false
        }
      )

      // 清除模糊相关缓存（因为背景已改变）
      this.clearBlurCache()

      this.showToast('背景替换完成', 'success')
      return result

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '背景替换失败'
      this.showToast(errorMessage, 'error')
      throw error
    } finally {
      this.setProcessingState(false)
    }
  }

  private async executeTransparentBackground(): Promise<ProcessingResult> {
    this.setProcessingState(true)

    try {
      const foregroundImageUrl = await this.ensureForegroundAvailable()
      
      const result = this.createResult(
        'transparent-background',
        foregroundImageUrl,
        { transparent: true }
      )

      this.showToast('背景已移除', 'success')
      return result

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '背景移除失败'
      this.showToast(errorMessage, 'error')
      throw error
    } finally {
      this.setProcessingState(false)
    }
  }

  private async ensureForegroundAvailable(): Promise<string> {
    const currentResult = this.getCurrentResult()
    const currentImage = this.validateCurrentImage()

    // 检查是否需要移除背景
    const needsBackgroundRemoval = this.shouldRemoveBackground(currentResult)

    if (needsBackgroundRemoval) {
      if (this.backgroundRemovalPlugin) {
        // 使用背景移除插件
        const sourceImageUrl = currentResult?.imageUrl || currentImage.url
        const sourceTimestamp = currentResult?.timestamp || 0
        
        const foregroundBase64 = await this.backgroundRemovalPlugin.ensureForegroundAvailable(
          sourceImageUrl,
          sourceTimestamp
        )
        
        return `data:image/png;base64,${foregroundBase64}`
      } else {
        // 直接调用API
        const { removeBackgroundSecure } = await import('../../lib/api-client')
        const sourceImageUrl = currentResult?.imageUrl || currentImage.url
        
        const canvas = this.createCanvas(currentImage.width, currentImage.height)
        const ctx = canvas.getContext('2d')!
        const img = await this.loadImage(sourceImageUrl)
        ctx.drawImage(img, 0, 0, currentImage.width, currentImage.height)
        
        const imageBase64 = this.canvasToBase64(canvas)
        const resultBase64 = await removeBackgroundSecure(imageBase64)
        
        return `data:image/png;base64,${resultBase64}`
      }
    } else {
      // 使用缓存的前景图片
      const cachedForeground = this.getCachedForeground()
      if (cachedForeground) {
        return cachedForeground
      }
      
      throw new Error('No foreground image available')
    }
  }

  private shouldRemoveBackground(currentResult: ProcessingResult | null): boolean {
    if (!currentResult) return true

    // 检查是否有缓存的前景图片，且时间戳有效
    const cachedForeground = this.getCachedForeground()
    if (!cachedForeground) return true

    const cachedTimestamp = this.getCache('foreground_timestamp') || 0
    return currentResult.timestamp > cachedTimestamp
  }

  private getCachedForeground(): string | null {
    if (this.backgroundRemovalPlugin) {
      const pureForeground = this.backgroundRemovalPlugin.getPureForeground()
      return pureForeground ? `data:image/png;base64,${pureForeground.imageBase64}` : null
    }
    
    return this.getCache('foreground_image')
  }

  private isTransparentBackground(backgroundUrl: string): boolean {
    // 检查是否是透明背景的标识
    return backgroundUrl.includes('transparent') || 
           backgroundUrl === 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAAC0lEQVQIHWNgAAIAAAUAAY27m/MAAAAASUVORK5CYII='
  }

  private clearBlurCache(): void {
    // 通知背景模糊插件清除缓存
    this.emit('clear:blur-cache')
  }

  // 获取默认背景列表
  getDefaultBackgrounds() {
    return getDefaultBackgrounds()
  }
}

// 背景选择器包装组件
const BackgroundSelectorWrapper: React.FC<ToolbarItemProps> = ({ context, disabled }) => {
  const handleBackgroundSelect = async (backgroundUrl: string) => {
    try {
      await context.executeAction?.('background-replacement', 'replace-background', {
        backgroundUrl
      })
    } catch (error) {
      console.error('Failed to replace background:', error)
    }
  }

  return (
    <BackgroundSelector
      onBackgroundSelect={handleBackgroundSelect}
      disabled={disabled}
    />
  )
}
