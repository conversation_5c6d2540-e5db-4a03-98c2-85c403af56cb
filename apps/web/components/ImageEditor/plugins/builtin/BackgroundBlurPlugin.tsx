// 背景模糊插件
import React, { useState } from 'react'
import { Blur } from 'lucide-react'
import { BasePlugin } from '../BasePlugin'
import { blurBackgroundSecure, adjustBlurIntensity } from '../../lib/background-removal'
import { BackgroundRemovalPlugin } from './BackgroundRemovalPlugin'
import type { PluginAction, ActionOption, ProcessingResult, ToolbarItem, ToolbarItemProps } from '../types'
import { Slider } from '../../ui/slider'
import { Label } from '../../ui/label'

export class BackgroundBlurPlugin extends BasePlugin {
  readonly id = 'background-blur'
  readonly name = '背景模糊'
  readonly version = '1.0.0'
  readonly description = '对图片背景应用模糊效果，保持前景清晰'
  readonly author = 'Faith Magic'
  readonly icon = Blur

  private backgroundRemovalPlugin?: BackgroundRemovalPlugin

  protected async onInitialize(): Promise<void> {
    // 获取背景移除插件的引用
    this.backgroundRemovalPlugin = this.context?.getPlugin?.('background-removal') as BackgroundRemovalPlugin
  }

  getActions(): PluginAction[] {
    return [
      {
        id: 'blur-background',
        name: '模糊背景',
        description: '对图片背景应用模糊效果',
        icon: Blur,
        category: 'background',
        execute: this.executeBlurBackground.bind(this),
        isAvailable: this.isBlurBackgroundAvailable.bind(this),
        getOptions: this.getBlurBackgroundOptions.bind(this)
      },
      {
        id: 'adjust-blur',
        name: '调整模糊强度',
        description: '实时调整背景模糊强度',
        icon: Blur,
        category: 'background',
        execute: this.executeAdjustBlur.bind(this),
        isAvailable: this.isAdjustBlurAvailable.bind(this),
        getOptions: this.getAdjustBlurOptions.bind(this)
      }
    ]
  }

  getToolbarItems(): ToolbarItem[] {
    return [
      {
        id: 'blur-intensity-slider',
        name: '模糊强度',
        icon: Blur,
        component: BlurIntensitySlider,
        position: 'right',
        order: 10
      }
    ]
  }

  private isBlurBackgroundAvailable(): boolean {
    return this.getCurrentImage() !== null
  }

  private isAdjustBlurAvailable(): boolean {
    const blurData = this.getCache('blur_data')
    return blurData !== null
  }

  private getBlurBackgroundOptions(): ActionOption[] {
    return [
      {
        key: 'blurIntensity',
        name: '模糊强度',
        type: 'slider',
        defaultValue: 20,
        min: 0,
        max: 100,
        step: 5,
        description: '背景模糊强度，0为无模糊，100为最大模糊'
      }
    ]
  }

  private getAdjustBlurOptions(): ActionOption[] {
    return [
      {
        key: 'blurIntensity',
        name: '模糊强度',
        type: 'slider',
        defaultValue: 20,
        min: 0,
        max: 100,
        step: 1,
        description: '实时调整背景模糊强度'
      }
    ]
  }

  private async executeBlurBackground(context: any, options: any = {}): Promise<ProcessingResult> {
    const currentImage = this.validateCurrentImage()
    const { blurIntensity = 20 } = options

    this.setProcessingState(true)

    try {
      // 检查是否有有效的缓存数据
      const existingBlurData = this.getCache('blur_data')
      const currentResult = this.getCurrentResult()
      
      const isCacheValid = existingBlurData && this.isCacheValid(existingBlurData, currentResult)

      if (isCacheValid) {
        // 使用缓存数据进行实时调整
        const resultBase64 = await adjustBlurIntensity(
          existingBlurData.originalImageBase64,
          existingBlurData.removedBackgroundBase64,
          blurIntensity
        )

        const result = this.createResult(
          'blur-background',
          `data:image/png;base64,${resultBase64}`,
          { blurIntensity, fromCache: true }
        )

        // 更新缓存中的当前强度
        this.setCache('blur_data', {
          ...existingBlurData,
          currentIntensity: blurIntensity
        })

        return result
      }

      // 需要重新生成模糊数据
      await this.generateBlurData(currentImage, currentResult)
      
      // 使用新生成的数据应用模糊
      const blurData = this.getCache('blur_data')
      const resultBase64 = await adjustBlurIntensity(
        blurData.originalImageBase64,
        blurData.removedBackgroundBase64,
        blurIntensity
      )

      const result = this.createResult(
        'blur-background',
        `data:image/png;base64,${resultBase64}`,
        { blurIntensity, fromCache: false },
        { blurData }
      )

      this.showToast('背景模糊完成', 'success')
      return result

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '背景模糊失败'
      this.showToast(errorMessage, 'error')
      throw error
    } finally {
      this.setProcessingState(false)
    }
  }

  private async executeAdjustBlur(context: any, options: any = {}): Promise<ProcessingResult> {
    const { blurIntensity = 20 } = options
    const blurData = this.getCache('blur_data')

    if (!blurData) {
      throw new Error('No blur data available. Please blur background first.')
    }

    const resultBase64 = await adjustBlurIntensity(
      blurData.originalImageBase64,
      blurData.removedBackgroundBase64,
      blurIntensity
    )

    // 更新缓存
    this.setCache('blur_data', {
      ...blurData,
      currentIntensity: blurIntensity
    })

    return this.createResult(
      'adjust-blur',
      `data:image/png;base64,${resultBase64}`,
      { blurIntensity, realtime: true }
    )
  }

  private isCacheValid(blurData: any, currentResult: ProcessingResult | null): boolean {
    if (!blurData) return false

    // 如果有新的处理结果，检查时间戳
    if (currentResult && currentResult.timestamp > blurData.timestamp) {
      return false
    }

    return true
  }

  private async generateBlurData(currentImage: any, currentResult: ProcessingResult | null): Promise<void> {
    // 确定源图片
    const sourceImageUrl = currentResult?.imageUrl || currentImage.url
    const sourceTimestamp = currentResult?.timestamp || 0

    // 生成原图的base64
    const canvas = this.createCanvas(currentImage.width, currentImage.height)
    const ctx = canvas.getContext('2d')!
    const img = await this.loadImage(sourceImageUrl)
    ctx.drawImage(img, 0, 0, currentImage.width, currentImage.height)
    const originalImageBase64 = this.canvasToBase64(canvas)

    // 获取或生成前景图片
    let removedBackgroundBase64: string

    if (this.backgroundRemovalPlugin) {
      removedBackgroundBase64 = await this.backgroundRemovalPlugin.ensureForegroundAvailable(
        sourceImageUrl,
        sourceTimestamp
      )
    } else {
      // 如果没有背景移除插件，直接调用API
      const { removeBackgroundSecure } = await import('../../lib/api-client')
      removedBackgroundBase64 = await removeBackgroundSecure(originalImageBase64)
    }

    // 缓存数据
    this.setCache('blur_data', {
      originalImageBase64,
      removedBackgroundBase64,
      currentIntensity: 20,
      timestamp: Date.now()
    })
  }

  // 获取当前模糊强度
  getCurrentBlurIntensity(): number {
    const blurData = this.getCache('blur_data')
    return blurData?.currentIntensity || 20
  }
}

// 模糊强度滑块组件
const BlurIntensitySlider: React.FC<ToolbarItemProps> = ({ context, disabled }) => {
  const [intensity, setIntensity] = useState(20)
  const [isAdjusting, setIsAdjusting] = useState(false)

  const handleIntensityChange = async (value: number[]) => {
    const newIntensity = value[0]
    setIntensity(newIntensity)
    
    if (!isAdjusting) {
      setIsAdjusting(true)
      try {
        // 执行实时调整
        await context.executeAction?.('background-blur', 'adjust-blur', { 
          blurIntensity: newIntensity 
        })
      } catch (error) {
        console.error('Failed to adjust blur:', error)
      } finally {
        setIsAdjusting(false)
      }
    }
  }

  return (
    <div className="space-y-2">
      <Label>模糊强度: {intensity}%</Label>
      <Slider
        value={[intensity]}
        onValueChange={handleIntensityChange}
        max={100}
        min={0}
        step={1}
        disabled={disabled || isAdjusting}
        className="w-full"
      />
    </div>
  )
}
