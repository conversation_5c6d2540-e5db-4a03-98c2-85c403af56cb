// 物体移除插件（Inpaint）
import React from 'react'
import { Eraser } from 'lucide-react'
import { BasePlugin } from '../BasePlugin'
import { removeObjectsSecure, base64ToFile } from '../../lib/api-client'
import { DEFAULT_NEGATIVE_PROMPT } from '../../lib/constant'
import type { PluginAction, ActionOption, ProcessingResult } from '../types'

export class ObjectRemovalPlugin extends BasePlugin {
  readonly id = 'object-removal'
  readonly name = '物体移除'
  readonly version = '1.0.0'
  readonly description = '使用AI技术智能移除图片中的物体'
  readonly author = 'Faith Magic'
  readonly icon = Eraser

  getActions(): PluginAction[] {
    return [
      {
        id: 'remove-objects',
        name: '移除物体',
        description: '使用画笔标记要移除的物体，AI将智能填充背景',
        icon: Eraser,
        category: 'object',
        execute: this.executeRemoveObjects.bind(this),
        isAvailable: this.isRemoveObjectsAvailable.bind(this),
        getOptions: this.getRemoveObjectsOptions.bind(this)
      }
    ]
  }

  private isRemoveObjectsAvailable(): boolean {
    const currentImage = this.getCurrentImage()
    const maskCanvas = this.getMaskCanvas()
    return currentImage !== null && maskCanvas !== null && this.hasMaskContent(maskCanvas)
  }

  private getMaskCanvas(): HTMLCanvasElement | null {
    // 通过事件系统获取mask canvas
    let maskCanvas: HTMLCanvasElement | null = null
    this.emit('request:mask-canvas', (canvas: HTMLCanvasElement) => {
      maskCanvas = canvas
    })
    return maskCanvas
  }

  private hasMaskContent(maskCanvas: HTMLCanvasElement): boolean {
    const ctx = maskCanvas.getContext('2d')
    if (!ctx) return false

    const imageData = ctx.getImageData(0, 0, maskCanvas.width, maskCanvas.height)
    const data = imageData.data

    // 检查是否有非透明像素
    for (let i = 3; i < data.length; i += 4) {
      if (data[i] > 0) return true
    }
    return false
  }

  private getRemoveObjectsOptions(): ActionOption[] {
    return [
      {
        key: 'prompt',
        name: '正向提示词',
        type: 'string',
        defaultValue: 'high quality, photorealistic, seamless background',
        description: '描述希望生成的内容特征'
      },
      {
        key: 'negative_prompt',
        name: '负向提示词',
        type: 'string',
        defaultValue: DEFAULT_NEGATIVE_PROMPT,
        description: '描述不希望出现的内容'
      },
      {
        key: 'baseUrl',
        name: 'API服务地址',
        type: 'string',
        defaultValue: 'https://faith1314666-imggen-magic-wand.hf.space',
        description: 'IOPaint服务器地址'
      }
    ]
  }

  private async executeRemoveObjects(context: any, options: any = {}): Promise<ProcessingResult> {
    const currentImage = this.validateCurrentImage()
    const maskCanvas = this.getMaskCanvas()
    
    if (!maskCanvas) {
      throw new Error('No mask canvas available')
    }

    if (!this.hasMaskContent(maskCanvas)) {
      throw new Error('Please draw a mask to indicate what to remove')
    }

    const {
      prompt = 'high quality, photorealistic, seamless background',
      negative_prompt = DEFAULT_NEGATIVE_PROMPT,
      baseUrl = 'https://faith1314666-imggen-magic-wand.hf.space'
    } = options

    this.setProcessingState(true)

    try {
      // 获取当前最终处理完的图片URL
      const currentResult = this.getCurrentResult()
      const sourceImageUrl = currentResult?.imageUrl || currentImage.url

      // 创建适合IOPaint的mask
      const processedMask = await this.createIOPaintMask(maskCanvas, currentImage)
      
      // 准备图片数据
      const imageBase64 = await this.getImageBase64(sourceImageUrl, currentImage)
      const maskBase64 = this.canvasToBase64(processedMask)

      // 转换为File对象
      const imageFile = base64ToFile(imageBase64, 'image.png', 'image/png')
      const maskFile = base64ToFile(maskBase64, 'mask.png', 'image/png')

      // 调用物体移除API
      const resultImageUrl = await removeObjectsSecure(
        {
          image: imageFile,
          mask: maskFile,
          prompt,
          negative_prompt
        },
        baseUrl
      )

      // 创建处理结果
      const result = this.createResult(
        'remove-objects',
        resultImageUrl,
        { 
          prompt, 
          negative_prompt, 
          baseUrl,
          sourceUrl: sourceImageUrl 
        }
      )

      // 清除相关缓存（因为图片内容已改变）
      this.clearRelatedCache()

      this.showToast('物体移除完成', 'success')
      return result

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '物体移除失败'
      this.showToast(errorMessage, 'error')
      throw error
    } finally {
      this.setProcessingState(false)
    }
  }

  private async createIOPaintMask(
    userMaskCanvas: HTMLCanvasElement, 
    currentImage: any
  ): Promise<HTMLCanvasElement> {
    // 创建适合IOPaint的mask canvas
    const maskCanvas = this.createCanvas(currentImage.width, currentImage.height)
    const maskCtx = maskCanvas.getContext('2d')!

    // IOPaint期望：白色=移除，黑色=保留
    // 首先填充黑色背景（保留区域）
    maskCtx.fillStyle = 'black'
    maskCtx.fillRect(0, 0, maskCanvas.width, maskCanvas.height)

    // 计算缩放比例
    const scaleX = currentImage.width / userMaskCanvas.width
    const scaleY = currentImage.height / userMaskCanvas.height

    // 获取用户绘制的mask数据
    const userMaskCtx = userMaskCanvas.getContext('2d')!
    const userMaskData = userMaskCtx.getImageData(
      0, 0, userMaskCanvas.width, userMaskCanvas.height
    )

    // 创建白色mask
    const whiteMaskCanvas = this.createCanvas(userMaskCanvas.width, userMaskCanvas.height)
    const whiteMaskCtx = whiteMaskCanvas.getContext('2d')!
    const whiteMaskData = whiteMaskCtx.createImageData(
      userMaskCanvas.width, userMaskCanvas.height
    )

    // 将用户的彩色mask转换为白色mask
    for (let i = 0; i < userMaskData.data.length; i += 4) {
      const alpha = userMaskData.data[i + 3]
      if (alpha > 0) {
        // 转换为白色
        whiteMaskData.data[i] = 255     // R
        whiteMaskData.data[i + 1] = 255 // G
        whiteMaskData.data[i + 2] = 255 // B
        whiteMaskData.data[i + 3] = alpha // A
      } else {
        // 保持透明
        whiteMaskData.data[i] = 0
        whiteMaskData.data[i + 1] = 0
        whiteMaskData.data[i + 2] = 0
        whiteMaskData.data[i + 3] = 0
      }
    }

    whiteMaskCtx.putImageData(whiteMaskData, 0, 0)

    // 将白色mask绘制到IOPaint mask上
    maskCtx.save()
    maskCtx.scale(scaleX, scaleY)
    maskCtx.globalCompositeOperation = 'source-over'
    maskCtx.drawImage(whiteMaskCanvas, 0, 0)
    maskCtx.restore()

    return maskCanvas
  }

  private async getImageBase64(imageUrl: string, currentImage: any): Promise<string> {
    const canvas = this.createCanvas(currentImage.width, currentImage.height)
    const ctx = canvas.getContext('2d')!
    
    const img = await this.loadImage(imageUrl)
    ctx.drawImage(img, 0, 0, currentImage.width, currentImage.height)
    
    return this.canvasToBase64(canvas)
  }

  private clearRelatedCache(): void {
    // 清除背景相关的缓存，因为inpaint操作改变了图片内容
    this.emit('clear:background-cache')
    
    // 清除自己的缓存
    this.clearCache()
  }

  // 为其他组件提供的辅助方法
  onImageChanged(): void {
    // 当图片改变时，清除mask相关的缓存
    this.clearCache('mask_data')
  }

  onResultChanged(): void {
    // 当有新的处理结果时，可能需要更新相关状态
    // 这里可以添加必要的逻辑
  }
}
