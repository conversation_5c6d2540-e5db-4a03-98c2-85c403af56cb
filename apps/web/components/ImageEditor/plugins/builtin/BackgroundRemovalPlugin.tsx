// 背景移除插件
import React from 'react'
import { Scissors } from 'lucide-react'
import { BasePlugin } from '../BasePlugin'
import { removeBackgroundSecure } from '../../lib/api-client'
import { canvasToBase64 } from '../../lib/background-removal'
import type { PluginAction, ActionOption, ProcessingResult } from '../types'

export class BackgroundRemovalPlugin extends BasePlugin {
  readonly id = 'background-removal'
  readonly name = '背景移除'
  readonly version = '1.0.0'
  readonly description = '使用AI技术智能移除图片背景'
  readonly author = 'Faith Magic'
  readonly icon = Scissors

  getActions(): PluginAction[] {
    return [
      {
        id: 'remove-background',
        name: '移除背景',
        description: '使用AI技术智能移除图片背景',
        icon: Scissors,
        category: 'background',
        execute: this.executeRemoveBackground.bind(this),
        isAvailable: this.isRemoveBackgroundAvailable.bind(this),
        getOptions: this.getRemoveBackgroundOptions.bind(this)
      }
    ]
  }

  private isRemoveBackgroundAvailable(): boolean {
    return this.getCurrentImage() !== null
  }

  private getRemoveBackgroundOptions(): ActionOption[] {
    return [
      {
        key: 'model',
        name: '模型选择',
        type: 'select',
        defaultValue: 'u2net',
        options: [
          { label: 'U2Net (通用)', value: 'u2net' },
          { label: 'U2Net+ (轻量)', value: 'u2netp' },
          { label: 'U2Net 人像', value: 'u2net_human_seg' },
          { label: 'U2Net 服装', value: 'u2net_cloth_seg' },
          { label: 'Silueta', value: 'silueta' },
          { label: 'ISNet 通用', value: 'isnet-general-use' },
          { label: 'RMBG-1.4', value: 'briaai/RMBG-1.4' }
        ],
        description: '选择背景移除模型，不同模型适用于不同类型的图片'
      }
    ]
  }

  private async executeRemoveBackground(context: any, options: any = {}): Promise<ProcessingResult> {
    const currentImage = this.validateCurrentImage()
    const { model = 'u2net' } = options

    this.setProcessingState(true)
    
    try {
      // 检查缓存
      const cacheKey = `removed_${model}_${currentImage.id}`
      const cachedResult = this.getCache(cacheKey)
      
      if (cachedResult) {
        this.showToast('使用缓存结果', 'info')
        return cachedResult
      }

      // 获取当前最终处理完的图片URL
      const currentResult = this.getCurrentResult()
      const sourceImageUrl = currentResult?.imageUrl || currentImage.url

      // 将图片转换为canvas并获取base64
      const canvas = this.createCanvas(currentImage.width, currentImage.height)
      const ctx = canvas.getContext('2d')!
      
      const img = await this.loadImage(sourceImageUrl)
      ctx.drawImage(img, 0, 0, currentImage.width, currentImage.height)
      
      const imageBase64 = this.canvasToBase64(canvas)

      // 调用背景移除API
      const resultBase64 = await removeBackgroundSecure(imageBase64, { model })
      const resultImageUrl = `data:image/png;base64,${resultBase64}`

      // 创建处理结果
      const result = this.createResult(
        'remove-background',
        resultImageUrl,
        { model, sourceUrl: sourceImageUrl },
        { originalBase64: imageBase64, removedBase64: resultBase64 }
      )

      // 缓存结果
      this.setCache(cacheKey, result)
      
      // 同时缓存纯前景图片（用于其他插件）
      this.setCache('pure_foreground', {
        imageBase64: resultBase64,
        timestamp: Date.now()
      })

      this.showToast('背景移除完成', 'success')
      return result

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '背景移除失败'
      this.showToast(errorMessage, 'error')
      throw error
    } finally {
      this.setProcessingState(false)
    }
  }

  // 获取纯前景图片（供其他插件使用）
  getPureForeground(): { imageBase64: string; timestamp: number } | null {
    return this.getCache('pure_foreground')
  }

  // 检查是否需要重新移除背景
  shouldRemoveBackground(sourceTimestamp: number): boolean {
    const pureForeground = this.getPureForeground()
    return !pureForeground || pureForeground.timestamp < sourceTimestamp
  }

  // 为其他插件提供的辅助方法
  async ensureForegroundAvailable(sourceImageUrl: string, sourceTimestamp: number): Promise<string> {
    if (!this.shouldRemoveBackground(sourceTimestamp)) {
      const cached = this.getPureForeground()
      if (cached) {
        return cached.imageBase64
      }
    }

    // 需要重新移除背景
    const currentImage = this.validateCurrentImage()
    
    const canvas = this.createCanvas(currentImage.width, currentImage.height)
    const ctx = canvas.getContext('2d')!
    
    const img = await this.loadImage(sourceImageUrl)
    ctx.drawImage(img, 0, 0, currentImage.width, currentImage.height)
    
    const imageBase64 = this.canvasToBase64(canvas)
    const resultBase64 = await removeBackgroundSecure(imageBase64)
    
    // 更新缓存
    this.setCache('pure_foreground', {
      imageBase64: resultBase64,
      timestamp: Date.now()
    })

    return resultBase64
  }
}
