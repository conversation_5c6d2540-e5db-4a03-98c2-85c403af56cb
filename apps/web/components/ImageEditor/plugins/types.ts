// 插件系统类型定义
// ImageData类型将从主模块导入

// 插件基础接口
export interface IPlugin {
  // 插件元信息
  id: string
  name: string
  version: string
  description: string
  author?: string
  icon?: React.ComponentType<any>

  // 插件生命周期
  initialize?(context: PluginContext): Promise<void> | void
  destroy?(): Promise<void> | void

  // 插件功能定义
  getActions(): PluginAction[]
  getToolbarItems?(): ToolbarItem[]
  getSettingsPanel?(): React.ComponentType<SettingsPanelProps> | null

  // 事件处理
  onImageChanged?(imageData: ImageData | null): void
  onResultChanged?(result: ProcessingResult | null): void
}

// 插件上下文 - 提供给插件的API
export interface PluginContext {
  // 图片数据访问
  getCurrentImage(): ImageData | null
  getCurrentResult(): ProcessingResult | null

  // 状态管理
  setProcessingState(pluginId: string, isProcessing: boolean): void
  getProcessingState(pluginId: string): boolean

  // 结果存储
  setResult(pluginId: string, result: ProcessingResult): void
  getResult(pluginId: string): ProcessingResult | null
  clearResult(pluginId: string): void

  // 缓存管理
  setCache(pluginId: string, key: string, data: any): void
  getCache(pluginId: string, key: string): any
  clearCache(pluginId: string, key?: string): void

  // 事件系统
  emit(event: string, data?: any): void
  on(event: string, handler: (data?: any) => void): () => void

  // UI交互
  showToast(
    message: string,
    type?: 'success' | 'error' | 'warning' | 'info'
  ): void
  showModal(component: React.ComponentType<any>, props?: any): void

  // API调用辅助
  callAPI(endpoint: string, options: RequestInit): Promise<Response>
}

// 插件动作定义
export interface PluginAction {
  id: string
  name: string
  description: string
  icon?: React.ComponentType<any>
  category: 'background' | 'object' | 'enhancement' | 'ai' | 'custom'

  // 动作执行
  execute(context: PluginContext, options?: any): Promise<ProcessingResult>

  // 动作可用性检查
  isAvailable?(context: PluginContext): boolean

  // 动作配置
  getOptions?(): ActionOption[]

  // 进度回调
  onProgress?(progress: number, message?: string): void
}

// 工具栏项定义
export interface ToolbarItem {
  id: string
  name: string
  icon?: React.ComponentType<any>
  component: React.ComponentType<ToolbarItemProps>
  position: 'top' | 'bottom' | 'left' | 'right'
  order: number
}

// 动作选项
export interface ActionOption {
  key: string
  name: string
  type: 'number' | 'string' | 'boolean' | 'select' | 'slider'
  defaultValue: any
  min?: number
  max?: number
  step?: number
  options?: { label: string; value: any }[]
  description?: string
}

// 处理结果
export interface ProcessingResult {
  id: string
  pluginId: string
  actionId: string
  imageUrl: string
  timestamp: number
  metadata?: Record<string, any>

  // 结果类型
  type: 'image' | 'mask' | 'data'

  // 缓存数据（用于快速调整）
  cacheData?: Record<string, any>
}

// 组件属性类型
export interface ToolbarItemProps {
  context: PluginContext
  disabled?: boolean
}

export interface SettingsPanelProps {
  context: PluginContext
  onSettingsChange?: (settings: Record<string, any>) => void
}

// 插件事件类型
export type PluginEvent =
  | 'image:changed'
  | 'result:changed'
  | 'processing:start'
  | 'processing:end'
  | 'processing:progress'
  | 'plugin:registered'
  | 'plugin:unregistered'
  | 'cache:cleared'
  | 'mask-canvas:set'
  | 'request:mask-canvas'
  | 'clear:background-cache'
  | 'clear:blur-cache'

// 插件配置
export interface PluginConfig {
  enabled: boolean
  settings: Record<string, any>
  order: number
}

// 插件管理器接口
export interface IPluginManager {
  // 插件注册
  register(plugin: IPlugin, config?: PluginConfig): Promise<void>
  unregister(pluginId: string): Promise<void>

  // 插件查询
  getPlugin(pluginId: string): IPlugin | null
  getAllPlugins(): IPlugin[]
  getEnabledPlugins(): IPlugin[]

  // 插件配置
  setPluginConfig(pluginId: string, config: Partial<PluginConfig>): void
  getPluginConfig(pluginId: string): PluginConfig | null

  // 动作执行
  executeAction(
    pluginId: string,
    actionId: string,
    options?: any
  ): Promise<ProcessingResult>

  // 事件系统
  emit(event: PluginEvent, data?: any): void
  on(event: PluginEvent, handler: (data?: any) => void): () => void

  // 生命周期
  initialize(): Promise<void>
  destroy(): Promise<void>

  // 结果管理
  getAllResults(): ProcessingResult[]
  getLatestResult(): ProcessingResult | null

  // 图片管理
  setCurrentImage(imageData: any): void
}
